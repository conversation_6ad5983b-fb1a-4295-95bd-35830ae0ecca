package main

import (
	"context"
	"fmt"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	// 初始化配置和数据库
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	fmt.Println("=== 检查Metro2日志数据 ===")
	
	// 用户ID
	testUserID := "6RDHxuNP6pT"
	
	ctx := context.Background()
	metro2LogsColl := gomongo.Coll("rr", "metro2logs")
	
	// 查找该用户的所有Metro2日志
	cursor, err := metro2LogsColl.Find(ctx, bson.M{"usrId": testUserID})
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer cursor.Close(ctx)
	
	fmt.Printf("用户 %s 的Metro2日志:\n", testUserID)
	
	count := 0
	for cursor.Next(ctx) {
		var log bson.M
		if err := cursor.Decode(&log); err != nil {
			fmt.Printf("❌ 解码失败: %v\n", err)
			continue
		}
		
		count++
		fmt.Printf("  日志 %d:\n", count)
		fmt.Printf("    ID: %v\n", log["_id"])
		fmt.Printf("    reportMonth: %v\n", log["reportMonth"])
		fmt.Printf("    生成时间: %v\n", log["generatedAt"])
		fmt.Printf("    文件名: %v\n", log["fileName"])
		fmt.Printf("    处理的租约数: %v\n", len(log["processedLeases"].(bson.A)))
		fmt.Println()
	}
	
	if count == 0 {
		fmt.Println("❌ 没有找到任何Metro2日志")
	} else {
		fmt.Printf("✅ 总共找到 %d 条Metro2日志\n", count)
	}
	
	// 按月份统计
	fmt.Println("\n=== 按月份统计 ===")
	months := []string{"2025-08", "2025-09", "2025-10"}
	
	for _, month := range months {
		count, err := metro2LogsColl.CountDocuments(ctx, bson.M{
			"usrId": testUserID,
			"reportMonth": month,
		})
		if err != nil {
			fmt.Printf("❌ 统计 %s 失败: %v\n", month, err)
			continue
		}
		fmt.Printf("  %s: %d 条记录\n", month, count)
	}
}
