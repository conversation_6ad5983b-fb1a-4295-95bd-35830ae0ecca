package main

import (
	"context"
	"fmt"
	"rent_report/entities"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("Failed to initialize environment: %v\n", err)
		return
	}

	ctx := context.Background()

	fmt.Println("=== Metro2月份逻辑修复测试 ===")

	// 测试用户ID（使用实际存在于Metro2记录中的用户ID）
	testUserID := "6RDHxuNP6pT"

	fmt.Printf("测试用户: %s\n", testUserID)

	// 显示当前时间信息
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)

	fmt.Printf("当前时间: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Printf("当前年月: %d-%02d\n", now.Year(), int(now.Month()))
	fmt.Printf("上个月年月: %d-%02d (Metro2查找目标)\n", lastMonth.Year(), int(lastMonth.Month()))

	// 1. 先直接测试CalculateUsageFromMetro2Logs函数
	fmt.Println("\n1. 直接测试CalculateUsageFromMetro2Logs函数")

	usedCount, err := entities.CalculateUsageFromMetro2Logs(ctx, testUserID, lastMonth.Year(), int(lastMonth.Month()))
	if err != nil {
		fmt.Printf("Error in CalculateUsageFromMetro2Logs: %v\n", err)
		return
	}

	fmt.Printf("直接调用结果 - 使用量: %d\n", usedCount)

	// 2. 测试完整的GetUsageStatisticsFromMetro2函数
	fmt.Println("\n2. 测试完整的GetUsageStatisticsFromMetro2函数")

	stats, err := entities.GetUsageStatisticsFromMetro2(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error getting usage statistics: %v\n", err)
		return
	}

	fmt.Printf("基础配额: %d\n", stats.BaseQuota)
	fmt.Printf("已使用: %d\n", stats.UsedCount)
	fmt.Printf("超量数量: %d\n", stats.OverageCount)
	fmt.Printf("剩余配额: %d\n", stats.RemainingQuota)
	fmt.Printf("超量费率: $%.2f %s/个\n", stats.OverageRate, stats.Currency)

	// 计算预期的超量费用
	totalOverageFee := float64(stats.OverageCount) * stats.OverageRate
	fmt.Printf("总超量费用: $%.2f %s\n", totalOverageFee, stats.Currency)

	// 3. 验证结果
	fmt.Println("\n3. 验证修复结果")

	if usedCount > 0 {
		fmt.Printf("✅ 成功找到Metro2记录，使用量: %d\n", usedCount)

		if stats.OverageCount > 0 {
			fmt.Printf("✅ 正确计算超量: %d个\n", stats.OverageCount)
			fmt.Printf("✅ 应产生费用: $%.2f %s\n", totalOverageFee, stats.Currency)
		} else {
			fmt.Printf("ℹ️  未超出配额，无额外费用\n")
		}
	} else {
		fmt.Printf("❌ 仍未找到Metro2记录，使用量为0\n")
		fmt.Printf("   请检查是否存在 %d-%02d 月份的Metro2记录\n", lastMonth.Year(), int(lastMonth.Month()))
	}

	// 4. 显示预期vs实际
	fmt.Println("\n4. 预期vs实际对比")
	fmt.Printf("预期查找月份: %d-%02d\n", lastMonth.Year(), int(lastMonth.Month()))
	fmt.Printf("预期使用量: 9个lease (基于你提供的Metro2记录)\n")
	fmt.Printf("预期超量: 9 - %d = %d个\n", stats.BaseQuota, 9-stats.BaseQuota)
	fmt.Printf("预期费用: %d × $%.2f = $%.2f\n", 9-stats.BaseQuota, stats.OverageRate, float64(9-stats.BaseQuota)*stats.OverageRate)

	fmt.Printf("\n实际查找月份: %d-%02d\n", lastMonth.Year(), int(lastMonth.Month()))
	fmt.Printf("实际使用量: %d个\n", stats.UsedCount)
	fmt.Printf("实际超量: %d个\n", stats.OverageCount)
	fmt.Printf("实际费用: $%.2f\n", totalOverageFee)

	// 5. 测试结果判断
	fmt.Println("\n=== 测试结果 ===")

	if usedCount == 9 && stats.OverageCount == 6 {
		fmt.Println("🎉 修复成功！")
		fmt.Println("- ✅ 正确查找上个月的Metro2记录")
		fmt.Println("- ✅ 正确计算使用量(9个)")
		fmt.Println("- ✅ 正确计算超量(6个)")
		fmt.Println("- ✅ 正确计算费用($6.00)")
	} else if usedCount > 0 {
		fmt.Println("⚠️  部分修复成功")
		fmt.Printf("- ✅ 找到了Metro2记录(使用量: %d)\n", usedCount)
		fmt.Printf("- ⚠️  但数量可能不匹配预期\n")
	} else {
		fmt.Println("❌ 修复未生效")
		fmt.Println("- ❌ 仍未找到Metro2记录")
		fmt.Println("- 💡 可能需要检查数据库中的记录格式")
	}
}

func initializeEnvironment() error {
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	return nil
}
