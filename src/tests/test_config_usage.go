package main

import (
	"context"
	"fmt"
	"rent_report/entities"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

// getBaseQuotaFromConfig 从配置文件获取基础配额
func getBaseQuotaFromConfig() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 20 // 默认值
}

// getOverageRateFromConfig 从配置文件获取超量费率
func getOverageRateFromConfig() int {
	if rate := goconfig.Config("rentReportUsage.overageRateCAD"); rate != nil {
		if rateInt, ok := rate.(int); ok {
			return rateInt
		}
		if rateInt64, ok := rate.(int64); ok {
			return int(rateInt64)
		}
	}
	return 100 // 默认值：$1.00 CAD = 100分
}

// getStripeConfig 从配置文件获取Stripe配置
func getStripeConfig() (string, string) {
	productId := "prod_SqeFaz9P1fDdOq" // 默认值
	if configProductId := goconfig.Config("stripe.extra_report_product_id"); configProductId != nil {
		if productIdStr, ok := configProductId.(string); ok && productIdStr != "" {
			productId = productIdStr
		}
	}

	priceId := "price_1RuwwtRdRW2qyPyrLm0nLLG0" // 默认值
	if configPriceId := goconfig.Config("stripe.extra_report_price_id"); configPriceId != nil {
		if priceIdStr, ok := configPriceId.(string); ok && priceIdStr != "" {
			priceId = priceIdStr
		}
	}

	return productId, priceId
}

func main() {
	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("Failed to initialize environment: %v\n", err)
		return
	}

	ctx := context.Background()

	fmt.Println("=== 配置文件使用量测试 ===")

	// 1. 测试配置读取
	fmt.Println("\n1. 测试配置读取")
	baseQuota := getBaseQuotaFromConfig()
	overageRate := getOverageRateFromConfig()
	productId, priceId := getStripeConfig()

	fmt.Printf("基础配额: %d\n", baseQuota)
	fmt.Printf("超量费率: %d 分 ($%.2f CAD)\n", overageRate, float64(overageRate)/100)
	fmt.Printf("Stripe产品ID: %s\n", productId)
	fmt.Printf("Stripe价格ID: %s\n", priceId)

	// 2. 测试使用量记录创建
	fmt.Println("\n2. 测试使用量记录创建")
	testUserID := "config_test_user"

	usage, err := entities.GetOrCreateUsageRecord(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error creating usage record: %v\n", err)
		return
	}

	fmt.Printf("记录ID: %s (使用nanoId)\n", usage.ID)
	fmt.Printf("用户ID: %s\n", usage.UID)
	fmt.Printf("基础配额: %d (从配置读取)\n", usage.BaseQuota)
	fmt.Printf("年月: %d-%02d\n", usage.Year, usage.Month)

	// 3. 测试Metro2统计
	fmt.Println("\n3. 测试Metro2统计")
	stats, err := entities.GetUsageStatisticsFromMetro2(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error getting Metro2 statistics: %v\n", err)
		return
	}

	fmt.Printf("基础配额: %d (从配置读取)\n", stats.BaseQuota)
	fmt.Printf("已使用: %d\n", stats.UsedCount)
	fmt.Printf("超量数量: %d\n", stats.OverageCount)
	fmt.Printf("剩余配额: %d\n", stats.RemainingQuota)
	fmt.Printf("超量费率: $%.2f %s/个 (从配置读取)\n", stats.OverageRate, stats.Currency)

	// 4. 模拟超量场景
	fmt.Println("\n4. 模拟超量场景")
	simulatedUsage := baseQuota + 3 // 超出3个
	simulatedOverage := simulatedUsage - baseQuota
	simulatedFee := float64(simulatedOverage) * float64(overageRate) / 100

	fmt.Printf("模拟使用量: %d\n", simulatedUsage)
	fmt.Printf("模拟超量: %d\n", simulatedOverage)
	fmt.Printf("模拟费用: $%.2f CAD\n", simulatedFee)

	// 5. 验证配置的有效性
	fmt.Println("\n5. 验证配置")

	if baseQuota > 0 && baseQuota <= 100 {
		fmt.Printf("✓ 基础配额合理: %d\n", baseQuota)
	} else {
		fmt.Printf("⚠ 基础配额可能不合理: %d\n", baseQuota)
	}

	if overageRate > 0 && overageRate <= 1000 {
		fmt.Printf("✓ 超量费率合理: %d 分\n", overageRate)
	} else {
		fmt.Printf("⚠ 超量费率可能不合理: %d 分\n", overageRate)
	}

	if len(productId) > 10 && len(priceId) > 10 {
		fmt.Printf("✓ Stripe ID格式正确\n")
	} else {
		fmt.Printf("⚠ Stripe ID格式可能有问题\n")
	}

	fmt.Println("\n=== 配置测试完成 ===")

	fmt.Println("\n配置验证结果:")
	fmt.Printf("- 基础配额: %d (测试环境设为较小值便于测试)\n", baseQuota)
	fmt.Printf("- 超量费率: $%.2f CAD/个\n", float64(overageRate)/100)
	fmt.Printf("- 所有配置都从local.ini读取，无硬编码\n")
	fmt.Printf("- 使用量记录ID使用nanoId格式\n")
	fmt.Printf("- 支持从Metro2日志计算使用量\n")

	fmt.Println("\n🎉 配置系统工作正常！")
}

func initializeEnvironment() error {
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	return nil
}
