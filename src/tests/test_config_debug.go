package main

import (
	"fmt"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

func main() {
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	if err := golog.InitLog(); err != nil {
		fmt.Printf("Failed to initialize logging: %v\n", err)
		return
	}

	fmt.Println("=== 配置调试测试 ===")

	// 测试各种配置读取方式
	fmt.Println("\n1. 直接读取配置")
	
	// 测试rentReportUsage.baseQuota
	baseQuotaRaw := goconfig.Config("rentReportUsage.baseQuota")
	fmt.Printf("rentReportUsage.baseQuota (raw): %v (type: %T)\n", baseQuotaRaw, baseQuotaRaw)
	
	if baseQuotaRaw != nil {
		if quotaInt, ok := baseQuotaRaw.(int); ok {
			fmt.Printf("baseQuota as int: %d\n", quotaInt)
		} else if quotaInt64, ok := baseQuotaRaw.(int64); ok {
			fmt.Printf("baseQuota as int64: %d\n", quotaInt64)
		} else if quotaFloat, ok := baseQuotaRaw.(float64); ok {
			fmt.Printf("baseQuota as float64: %f\n", quotaFloat)
		} else if quotaStr, ok := baseQuotaRaw.(string); ok {
			fmt.Printf("baseQuota as string: %s\n", quotaStr)
		} else {
			fmt.Printf("baseQuota unknown type: %T\n", baseQuotaRaw)
		}
	} else {
		fmt.Printf("baseQuota is nil\n")
	}

	// 测试rentReportUsage.overageRateCAD
	overageRateRaw := goconfig.Config("rentReportUsage.overageRateCAD")
	fmt.Printf("rentReportUsage.overageRateCAD (raw): %v (type: %T)\n", overageRateRaw, overageRateRaw)
	
	if overageRateRaw != nil {
		if rateInt, ok := overageRateRaw.(int); ok {
			fmt.Printf("overageRate as int: %d\n", rateInt)
		} else if rateInt64, ok := overageRateRaw.(int64); ok {
			fmt.Printf("overageRate as int64: %d\n", rateInt64)
		} else if rateFloat, ok := overageRateRaw.(float64); ok {
			fmt.Printf("overageRate as float64: %f\n", rateFloat)
		} else if rateStr, ok := overageRateRaw.(string); ok {
			fmt.Printf("overageRate as string: %s\n", rateStr)
		} else {
			fmt.Printf("overageRate unknown type: %T\n", overageRateRaw)
		}
	} else {
		fmt.Printf("overageRate is nil\n")
	}

	// 测试Stripe配置
	fmt.Println("\n2. 测试Stripe配置")
	
	productIdRaw := goconfig.Config("stripe.extra_report_product_id")
	fmt.Printf("stripe.extra_report_product_id: %v (type: %T)\n", productIdRaw, productIdRaw)
	
	priceIdRaw := goconfig.Config("stripe.extra_report_price_id")
	fmt.Printf("stripe.extra_report_price_id: %v (type: %T)\n", priceIdRaw, priceIdRaw)
	
	secretKeyRaw := goconfig.Config("stripe.secret_key")
	if secretKeyRaw != nil {
		if secretStr, ok := secretKeyRaw.(string); ok {
			fmt.Printf("stripe.secret_key: %s... (length: %d)\n", secretStr[:10], len(secretStr))
		}
	} else {
		fmt.Printf("stripe.secret_key: nil\n")
	}

	// 测试其他已知配置
	fmt.Println("\n3. 测试其他已知配置")
	
	serverPortRaw := goconfig.Config("server.port")
	fmt.Printf("server.port: %v (type: %T)\n", serverPortRaw, serverPortRaw)
	
	dbUriRaw := goconfig.Config("dbs.rr.uri")
	if dbUriRaw != nil {
		if uriStr, ok := dbUriRaw.(string); ok {
			fmt.Printf("dbs.rr.uri: %s... (length: %d)\n", uriStr[:20], len(uriStr))
		}
	} else {
		fmt.Printf("dbs.rr.uri: nil\n")
	}

	fmt.Println("\n=== 配置调试完成 ===")
}
