package main

import (
	"context"
	"fmt"
	"rent_report/services"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/v75"
)

func main() {
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		} else {
			fmt.Printf("❌ Stripe密钥格式错误\n")
			return
		}
	} else {
		fmt.Printf("❌ 未找到Stripe密钥配置\n")
		return
	}

	ctx := context.Background()
	testUserID := "6RDHxuNP6pT"
	year := 2025
	month := 8

	fmt.Printf("=== 手动上报8月份超量使用 ===\n")

	usageService := services.NewMonthlyUsageReportService()

	// 1. 再次确认超量使用
	fmt.Printf("1. 确认8月份超量使用\n")
	overageCount, err := usageService.CalculateUserUsage(ctx, testUserID, year, month)
	if err != nil {
		fmt.Printf("❌ 计算超量失败: %v\n", err)
		return
	}

	if overageCount == 0 {
		fmt.Printf("✅ 没有超量使用，无需上报\n")
		return
	}

	fmt.Printf("🔥 确认超量使用: %d 个lease\n", overageCount)

	// 2. 获取Stripe客户ID
	fmt.Printf("\n2. 获取Stripe客户信息\n")
	customerID, err := usageService.GetStripeCustomerIDForTesting(ctx, testUserID)
	if err != nil {
		fmt.Printf("❌ 获取Stripe客户ID失败: %v\n", err)
		return
	}
	fmt.Printf("✅ Stripe客户ID: %s\n", customerID)

	// 3. 询问用户确认
	fmt.Printf("\n⚠️  准备向Stripe上报以下信息:\n")
	fmt.Printf("   用户ID: %s\n", testUserID)
	fmt.Printf("   Stripe客户ID: %s\n", customerID)
	fmt.Printf("   超量使用: %d 个lease\n", overageCount)
	fmt.Printf("   月份: %04d-%02d\n", year, month)
	fmt.Printf("\n是否继续？(y/N): ")

	var response string
	fmt.Scanln(&response)

	if response != "y" && response != "Y" {
		fmt.Printf("❌ 用户取消操作\n")
		return
	}

	// 4. 执行上报
	fmt.Printf("\n3. 向Stripe上报超量使用\n")
	err = usageService.ReportSingleUserUsage(ctx, testUserID, overageCount, year, month)
	if err != nil {
		fmt.Printf("❌ 上报失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 成功向Stripe上报8月份超量使用！\n")
	fmt.Printf("\n📋 后续步骤:\n")
	fmt.Printf("1. 🔍 检查Stripe Dashboard中的usage records\n")
	fmt.Printf("2. 📅 确认下次invoice会包含这些超量费用\n")
	fmt.Printf("3. 🎯 监控9月11日的续订是否包含额外费用\n")

	fmt.Printf("\n🎉 8月份超量使用上报完成！\n")
}
