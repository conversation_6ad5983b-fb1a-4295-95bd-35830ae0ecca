package main

import (
	"context"
	"fmt"
	"rent_report/entities"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("Failed to initialize environment: %v\n", err)
		return
	}

	ctx := context.Background()

	fmt.Println("=== 租赁报告使用量测试场景（基于Metro2日志）===")

	// 测试场景：不同的用户
	testScenarios := []struct {
		userID      string
		description string
	}{
		{"user_metro2_test_1", "用户1 - 测试Metro2日志计算"},
		{"user_metro2_test_2", "用户2 - 测试Metro2日志计算"},
		{"user_metro2_test_3", "用户3 - 测试Metro2日志计算"},
	}

	fmt.Println("\n1. 创建测试用户")
	for _, scenario := range testScenarios {
		fmt.Printf("\n--- %s ---\n", scenario.description)

		// 创建测试用户
		err := createTestUser(ctx, scenario.userID)
		if err != nil {
			fmt.Printf("Error creating user: %v\n", err)
			continue
		}
		fmt.Printf("用户 %s 创建成功\n", scenario.userID)
	}

	fmt.Println("\n2. 测试从Metro2日志计算使用量")
	now := time.Now()
	year := now.Year()
	month := int(now.Month())

	for _, scenario := range testScenarios {
		fmt.Printf("\n--- 测试用户 %s ---\n", scenario.userID)

		// 测试从Metro2日志计算使用量
		usageCount, err := entities.CalculateUsageFromMetro2Logs(ctx, scenario.userID, year, month)
		if err != nil {
			fmt.Printf("Error calculating usage from Metro2 logs: %v\n", err)
			continue
		}

		fmt.Printf("从Metro2日志计算的使用量: %d\n", usageCount)

		// 获取基于Metro2的统计信息
		stats, err := entities.GetUsageStatisticsFromMetro2(ctx, scenario.userID)
		if err != nil {
			fmt.Printf("Error getting Metro2 statistics: %v\n", err)
			continue
		}

		fmt.Printf("基础配额: %d\n", stats.BaseQuota)
		fmt.Printf("已使用: %d\n", stats.UsedCount)
		fmt.Printf("超量数量: %d\n", stats.OverageCount)
		fmt.Printf("剩余配额: %d\n", stats.RemainingQuota)

		if stats.OverageCount > 0 {
			totalCharge := float64(stats.OverageCount) * stats.OverageRate
			fmt.Printf("超量费用: $%.2f %s\n", totalCharge, stats.Currency)
		} else {
			fmt.Printf("超量费用: $0.00 %s\n", stats.Currency)
		}

		// 检查使用量限制
		canEnable, _, err := entities.CheckUsageLimit(ctx, scenario.userID)
		if err != nil {
			fmt.Printf("Error checking limit: %v\n", err)
		} else {
			fmt.Printf("可以启用新报告: %t\n", canEnable)
		}
	}

	fmt.Println("\n3. 测试Metro2日志查询")
	fmt.Printf("查询月份: %d-%02d\n", year, month)

	// 检查是否有Metro2生成记录
	fmt.Println("注意：如果没有Metro2生成记录，所有用户的使用量都将为0")
	fmt.Println("要测试完整功能，需要先生成Metro2报告")

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n基于Metro2日志的使用量计算说明:")
	fmt.Println("1. 使用量直接从Metro2生成日志计算")
	fmt.Println("2. 按用户统计其在Metro2报告中的lease数量")
	fmt.Println("3. 支持多次生成时取最后一次记录")
	fmt.Println("4. 如果当月没有Metro2报告，使用量为0")
}

// initializeEnvironment 初始化环境
func initializeEnvironment() error {
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	return nil
}

// createTestUser 创建测试用户
func createTestUser(ctx context.Context, userID string) error {
	usersColl := gomongo.Coll("rr", "users")
	if usersColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// 检查用户是否存在
	var existingUser map[string]interface{}
	err := usersColl.FindOne(ctx, map[string]interface{}{"_id": userID}).Decode(&existingUser)

	if err != nil {
		// 用户不存在，创建测试用户
		testUser := map[string]interface{}{
			"_id":    userID,
			"email":  fmt.Sprintf("%<EMAIL>", userID),
			"usrNm":  fmt.Sprintf("Test User %s", userID),
			"acctTp": "vip", // 设置为VIP用户
			"role":   "normal_user",
			"status": "active",
		}
		_, err = usersColl.InsertOne(ctx, testUser)
		if err != nil {
			return fmt.Errorf("failed to create test user: %v", err)
		}
		fmt.Printf("创建测试用户: %s\n", userID)
	} else {
		// 用户存在，确保是VIP
		_, err = usersColl.UpdateOne(ctx,
			map[string]interface{}{"_id": userID},
			map[string]interface{}{"$set": map[string]interface{}{"acctTp": "vip"}})
		if err != nil {
			return fmt.Errorf("failed to update user to VIP: %v", err)
		}
		fmt.Printf("用户已存在，确保为VIP: %s\n", userID)
	}

	return nil
}
