package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"rent_report/controller"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go"
)

func main() {
	// 初始化配置和数据库
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		}
	}

	fmt.Println("=== 测试Checkout Session创建 ===")

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	r := gin.New()

	// 注册控制器
	checkoutController := &controller.CheckoutController{}
	checkoutController.RegisterRoutes(r)

	// 准备测试请求数据
	requestData := map[string]interface{}{
		"priceId":    "price_1Rv0DfRdRW2qyPyrPAl8K7m2", // 基础订阅价格
		"prdId":      "prod_SqeFaz9P1fDdOq",            // 产品ID
		"successUrl": "http://localhost:8089/success",
		"cancelUrl":  "http://localhost:8089/cancel",
	}

	jsonData, _ := json.Marshal(requestData)

	// 创建HTTP请求
	req, _ := http.NewRequest("POST", "/v1/checkout/session", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// 跳过JWT验证，直接测试Stripe API调用
	// 注意：这个测试主要验证Stripe line items的配置是否正确

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	fmt.Println("📤 发送checkout session创建请求...")
	r.ServeHTTP(w, req)

	// 检查响应
	fmt.Printf("📥 响应状态码: %d\n", w.Code)
	fmt.Printf("📥 响应内容: %s\n", w.Body.String())

	if w.Code == 200 {
		var response map[string]interface{}
		if err := json.Unmarshal(w.Body.Bytes(), &response); err == nil {
			if url, ok := response["url"].(string); ok {
				fmt.Printf("✅ Checkout session创建成功!\n")
				fmt.Printf("🔗 Checkout URL: %s\n", url)
			}
		}
	} else {
		fmt.Printf("❌ Checkout session创建失败\n")

		// 尝试解析错误信息
		var errorResponse map[string]interface{}
		if err := json.Unmarshal(w.Body.Bytes(), &errorResponse); err == nil {
			if errorMsg, ok := errorResponse["error"].(string); ok {
				fmt.Printf("错误信息: %s\n", errorMsg)
			}
		}
	}
}
