package main

import (
	"fmt"

	"github.com/real-rm/goconfig"
)

func main() {
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	fmt.Printf("=== 调试配额配置读取 ===\n")

	// 1. 直接读取配置
	fmt.Printf("1. 直接读取配置\n")
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		fmt.Printf("✅ 原始值: %v\n", quota)
		fmt.Printf("✅ 类型: %T\n", quota)
		
		// 尝试各种类型转换
		if quotaInt, ok := quota.(int); ok {
			fmt.Printf("✅ 转换为int成功: %d\n", quotaInt)
		} else {
			fmt.Printf("❌ 转换为int失败\n")
		}
		
		if quotaInt64, ok := quota.(int64); ok {
			fmt.Printf("✅ 转换为int64成功: %d\n", quotaInt64)
		} else {
			fmt.Printf("❌ 转换为int64失败\n")
		}
		
		if quotaFloat64, ok := quota.(float64); ok {
			fmt.Printf("✅ 转换为float64成功: %f\n", quotaFloat64)
		} else {
			fmt.Printf("❌ 转换为float64失败\n")
		}
		
		if quotaString, ok := quota.(string); ok {
			fmt.Printf("✅ 转换为string成功: %s\n", quotaString)
		} else {
			fmt.Printf("❌ 转换为string失败\n")
		}
	} else {
		fmt.Printf("❌ 未找到rentReportUsage.baseQuota配置\n")
	}

	// 2. 模拟getBaseQuota函数的逻辑
	fmt.Printf("\n2. 模拟getBaseQuota函数逻辑\n")
	getBaseQuota := func() int {
		if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
			fmt.Printf("   配置值存在: %v (类型: %T)\n", quota, quota)
			
			if quotaInt, ok := quota.(int); ok {
				fmt.Printf("   ✅ 成功转换为int: %d\n", quotaInt)
				return quotaInt
			}
			if quotaInt64, ok := quota.(int64); ok {
				fmt.Printf("   ✅ 成功转换为int64: %d\n", quotaInt64)
				return int(quotaInt64)
			}
			
			// 尝试从float64转换（INI文件可能解析为float64）
			if quotaFloat64, ok := quota.(float64); ok {
				result := int(quotaFloat64)
				fmt.Printf("   ✅ 从float64转换: %f -> %d\n", quotaFloat64, result)
				return result
			}
			
			fmt.Printf("   ❌ 无法转换，使用默认值\n")
		} else {
			fmt.Printf("   ❌ 配置不存在，使用默认值\n")
		}
		return 20 // 默认值
	}

	result := getBaseQuota()
	fmt.Printf("最终结果: %d\n", result)

	// 3. 检查其他相关配置
	fmt.Printf("\n3. 检查其他rentReportUsage配置\n")
	if overageRate := goconfig.Config("rentReportUsage.overageRateCAD"); overageRate != nil {
		fmt.Printf("overageRateCAD: %v (类型: %T)\n", overageRate, overageRate)
	}
}
