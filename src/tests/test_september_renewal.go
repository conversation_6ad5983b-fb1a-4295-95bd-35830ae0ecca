package main

import (
	"context"
	"fmt"
	"rent_report/services"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/v75"
)

func main() {
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		} else {
			fmt.Printf("❌ Stripe密钥格式错误\n")
			return
		}
	} else {
		fmt.Printf("❌ 未找到Stripe密钥配置\n")
		return
	}

	ctx := context.Background()
	testUserID := "6RDHxuNP6pT"
	year := 2025
	month := 9 // 测试9月份

	fmt.Printf("=== 测试9月份续订流程 ===\n")

	// 1. 检查9月份是否有Metro2记录
	fmt.Printf("1. 检查9月份Metro2记录\n")
	usageService := services.NewMonthlyUsageReportService()
	overageCount, err := usageService.CalculateUserUsage(ctx, testUserID, year, month)
	if err != nil {
		fmt.Printf("❌ 计算9月份使用量失败: %v\n", err)
		// 这是预期的，因为9月份还没有Metro2记录
		fmt.Printf("✅ 符合预期：9月份还没有Metro2记录\n")
		overageCount = 0
	} else {
		fmt.Printf("✅ 9月份使用量: %d 个超量\n", overageCount)
	}

	// 2. 模拟9月份续订 - 应该不包含extra fee
	fmt.Printf("\n2. 模拟9月份续订（应该没有extra fee）\n")
	if overageCount > 0 {
		fmt.Printf("⚠️  警告：9月份检测到 %d 个超量使用，这可能不正确\n", overageCount)

		// 尝试上报到Stripe
		err = usageService.ReportSingleUserUsage(ctx, testUserID, overageCount, year, month)
		if err != nil {
			fmt.Printf("❌ 向Stripe上报9月份使用量失败: %v\n", err)
		} else {
			fmt.Printf("✅ 成功向Stripe上报9月份 %d 个超量使用\n", overageCount)
		}
	} else {
		fmt.Printf("✅ 9月份没有超量使用，续订应该只包含基础费用 $19.99\n")
	}

	// 3. 检查当前Stripe订阅状态
	fmt.Printf("\n3. 检查Stripe订阅状态\n")
	customerID, err := usageService.GetStripeCustomerIDForTesting(ctx, testUserID)
	if err != nil {
		fmt.Printf("❌ 获取Stripe客户ID失败: %v\n", err)
		return
	}
	fmt.Printf("✅ Stripe客户ID: %s\n", customerID)

	// 4. 建议下一步操作
	fmt.Printf("\n=== 测试建议 ===\n")
	fmt.Printf("1. 🔍 检查Stripe Dashboard中的upcoming invoice\n")
	fmt.Printf("2. 📅 确认9月11日的续订只包含 $19.99 基础费用\n")
	fmt.Printf("3. 🎯 如果需要测试有超量的情况，可以：\n")
	fmt.Printf("   - 创建9月份的Metro2记录\n")
	fmt.Printf("   - 或者手动调用上报函数\n")

	fmt.Printf("\n🎉 9月份续订测试完成！\n")
}
