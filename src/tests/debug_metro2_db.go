package main

import (
	"context"
	"fmt"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("Failed to initialize environment: %v\n", err)
		return
	}

	ctx := context.Background()
	
	fmt.Println("=== 调试Metro2记录 ===")
	
	// 1. 查看所有Metro2生成记录
	fmt.Println("\n1. 查看所有Metro2生成记录")
	
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		fmt.Println("❌ metro2_generation_logs collection not initialized")
		return
	}
	
	cursor, err := coll.Find(ctx, bson.M{})
	if err != nil {
		fmt.Printf("❌ Failed to query metro2 generation logs: %v\n", err)
		return
	}
	defer cursor.Close(ctx)
	
	var allLogs []bson.M
	if err = cursor.All(ctx, &allLogs); err != nil {
		fmt.Printf("❌ Failed to decode metro2 generation logs: %v\n", err)
		return
	}
	
	fmt.Printf("找到 %d 条Metro2生成记录:\n", len(allLogs))
	
	for i, log := range allLogs {
		fmt.Printf("\n记录 %d:\n", i+1)
		fmt.Printf("  ID: %v\n", log["_id"])
		fmt.Printf("  reportMonth: %v\n", log["reportMonth"])
		fmt.Printf("  totalLeases: %v\n", log["totalLeases"])
		fmt.Printf("  totalTenants: %v\n", log["totalTenants"])
		fmt.Printf("  generatedAt: %v (类型: %T)\n", log["generatedAt"], log["generatedAt"])
		
		// 检查processedLeases
		if processedLeases, ok := log["processedLeases"]; ok {
			if leases, ok := processedLeases.(bson.A); ok {
				fmt.Printf("  processedLeases: %d 个lease\n", len(leases))
			} else {
				fmt.Printf("  processedLeases: %v (类型: %T)\n", processedLeases, processedLeases)
			}
		} else {
			fmt.Printf("  processedLeases: 不存在\n")
		}
	}
	
	// 2. 专门查找2025-07的记录
	fmt.Println("\n2. 专门查找2025-07的记录")
	
	filter := bson.M{"reportMonth": "2025-07"}
	cursor2, err := coll.Find(ctx, filter)
	if err != nil {
		fmt.Printf("❌ Failed to query 2025-07 records: %v\n", err)
		return
	}
	defer cursor2.Close(ctx)
	
	var julyLogs []bson.M
	if err = cursor2.All(ctx, &julyLogs); err != nil {
		fmt.Printf("❌ Failed to decode 2025-07 records: %v\n", err)
		return
	}
	
	fmt.Printf("找到 %d 条2025-07的记录:\n", len(julyLogs))
	
	if len(julyLogs) > 0 {
		log := julyLogs[0] // 取第一条记录
		fmt.Printf("\n2025-07记录详情:\n")
		fmt.Printf("  ID: %v\n", log["_id"])
		fmt.Printf("  totalLeases: %v\n", log["totalLeases"])
		fmt.Printf("  generatedAt: %v\n", log["generatedAt"])
		
		// 检查processedLeases中的用户ID
		if processedLeases, ok := log["processedLeases"].(bson.A); ok {
			fmt.Printf("  包含 %d 个lease:\n", len(processedLeases))
			
			// 统计每个用户的lease数量
			userLeaseCount := make(map[string]int)
			
			for _, lease := range processedLeases {
				if leaseMap, ok := lease.(bson.M); ok {
					if leaseID, ok := leaseMap["leaseId"].(string); ok {
						// 查询这个lease的用户ID
						leaseColl := gomongo.Coll("rr", "leases")
						if leaseColl != nil {
							var leaseDoc struct {
								ID     string `bson:"_id"`
								UserID string `bson:"usrId"`
							}
							err := leaseColl.FindOne(ctx, bson.M{"_id": leaseID}).Decode(&leaseDoc)
							if err == nil {
								userLeaseCount[leaseDoc.UserID]++
							}
						}
					}
				}
			}
			
			fmt.Printf("  用户lease统计:\n")
			for userID, count := range userLeaseCount {
				fmt.Printf("    用户 %s: %d 个lease\n", userID, count)
			}
		}
	}
	
	// 3. 检查目标用户的lease
	fmt.Println("\n3. 检查目标用户的lease")
	testUserID := "45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
	
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		fmt.Println("❌ leases collection not initialized")
		return
	}
	
	leaseCursor, err := leaseColl.Find(ctx, bson.M{"usrId": testUserID})
	if err != nil {
		fmt.Printf("❌ Failed to query user leases: %v\n", err)
		return
	}
	defer leaseCursor.Close(ctx)
	
	var userLeases []bson.M
	if err = leaseCursor.All(ctx, &userLeases); err != nil {
		fmt.Printf("❌ Failed to decode user leases: %v\n", err)
		return
	}
	
	fmt.Printf("用户 %s 拥有 %d 个lease:\n", testUserID, len(userLeases))
	
	for i, lease := range userLeases {
		fmt.Printf("  lease %d: ID=%v, 状态=%v\n", i+1, lease["_id"], lease["status"])
	}
}

func initializeEnvironment() error {
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	return nil
}
