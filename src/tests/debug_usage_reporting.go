package main

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"rent_report/controller"
)

func main() {
	fmt.Println("=== 调试使用量上报 ===")
	
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化数据库
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to init MongoDB: %v\n", err)
		return
	}

	ctx := context.Background()
	
	// 创建Metro2控制器
	controller := &controller.Metro2ReportController{}
	
	// 测试当前月份（8月）的使用量上报
	currentMonth := time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)
	
	fmt.Printf("尝试上报 %s 的使用量...\n", currentMonth.Format("2006-01"))
	
	err := controller.ReportUsageAfterMetro2Generation(ctx, currentMonth)
	if err != nil {
		fmt.Printf("❌ 使用量上报失败: %v\n", err)
	} else {
		fmt.Printf("✅ 使用量上报成功\n")
	}
	
	// 也测试一下7月份的数据（我们看到有7月的Metro2记录）
	julyMonth := time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC)
	
	fmt.Printf("\n尝试上报 %s 的使用量...\n", julyMonth.Format("2006-01"))
	
	err = controller.ReportUsageAfterMetro2Generation(ctx, julyMonth)
	if err != nil {
		fmt.Printf("❌ 使用量上报失败: %v\n", err)
	} else {
		fmt.Printf("✅ 使用量上报成功\n")
	}
}
