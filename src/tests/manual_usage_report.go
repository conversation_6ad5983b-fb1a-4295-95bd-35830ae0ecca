package main

import (
	"context"
	"fmt"
	"rent_report/services"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/v75"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func main() {
	// 初始化配置和数据库
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		} else {
			fmt.Printf("❌ Stripe密钥格式错误\n")
			return
		}
	} else {
		fmt.Printf("❌ 未找到Stripe密钥配置\n")
		return
	}

	// 初始化日志（可选）
	// golog 通常会自动初始化

	fmt.Println("=== 手动上报9月份使用量（为10月续订做准备）===")

	// Landlord ID（从订阅记录中获取）
	testUserID := "6RDHxuNP6pT"

	// 9月份数据
	year := 2025
	month := 9

	ctx := context.Background()

	// 1. 先检查数据库中的所有Metro2记录
	fmt.Printf("1. 检查数据库中的所有Metro2记录\n")
	err := checkAllMetro2Records(ctx)
	if err != nil {
		fmt.Printf("❌ 检查Metro2记录失败: %v\n", err)
		return
	}

	// 2. 检查特定月份的记录
	fmt.Printf("\n2. 检查数据库中的Metro2记录 (reportMonth='%d-%02d')\n", year, month)
	err = checkMetro2Records(ctx, testUserID, year, month)
	if err != nil {
		fmt.Printf("❌ 检查Metro2记录失败: %v\n", err)
		return
	}

	// 2. 计算9月份使用量
	fmt.Printf("\n2. 计算用户 %s 在 %d-%02d 的使用量\n", testUserID, year, month)

	usageCount, err := calculateUsageFromMetro2Logs(ctx, testUserID, year, month)
	if err != nil {
		fmt.Printf("❌ 计算使用量失败: %v\n", err)
		return
	}

	// 2. 获取基础配额
	baseQuota := getBaseQuota()
	overageCount := 0
	if usageCount > baseQuota {
		overageCount = usageCount - baseQuota
	}

	fmt.Printf("✅ 使用量统计:\n")
	fmt.Printf("    基础配额: %d\n", baseQuota)
	fmt.Printf("    实际使用: %d\n", usageCount)
	fmt.Printf("    超量数量: %d\n", overageCount)

	if overageCount <= 0 {
		fmt.Println("✅ 无超量使用，无需上报")
		return
	}

	// 3. 手动上报到Stripe
	fmt.Printf("\n2. 手动向Stripe上报 %d 个超量使用\n", overageCount)

	usageService := services.NewMonthlyUsageReportService()
	err = usageService.ReportSingleUserUsage(ctx, testUserID, overageCount, year, month)
	if err != nil {
		fmt.Printf("❌ 向Stripe上报使用量失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 成功向Stripe上报使用量: %d个超量\n", overageCount)
	fmt.Println("\n🎉 手动上报完成！下次账单应该包含extra fee。")
}

// calculateUsageFromMetro2Logs 从Metro2日志计算使用量（按landlord统计）
func calculateUsageFromMetro2Logs(ctx context.Context, landlordID string, year, month int) (int, error) {
	metro2LogsColl := gomongo.Coll("rr", "metro2_generation_logs")
	if metro2LogsColl == nil {
		return 0, fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	// 构建查询条件
	reportMonth := fmt.Sprintf("%d-%02d", year, month)

	// 查找该月份的所有Metro2报告
	cursor, err := metro2LogsColl.Find(ctx, map[string]interface{}{
		"reportMonth": reportMonth,
	})
	if err != nil {
		return 0, fmt.Errorf("failed to find Metro2 logs: %v", err)
	}
	defer cursor.Close(ctx)

	landlordUsageCount := 0

	// 遍历每个Metro2报告
	for cursor.Next(ctx) {
		var record map[string]interface{}
		if err := cursor.Decode(&record); err != nil {
			continue
		}

		// 检查processedLeases数组 - 处理 primitive.A 类型
		processedLeases := record["processedLeases"]
		if processedLeases == nil {
			fmt.Printf("  processedLeases为空\n")
			continue
		}

		// 尝试转换为 primitive.A
		if primitiveA, ok := processedLeases.(primitive.A); ok {
			fmt.Printf("  processedLeases数组长度: %d (primitive.A)\n", len(primitiveA))
			for i, lease := range primitiveA {
				var leaseMap map[string]interface{}
				var ok bool

				// 尝试多种类型转换
				if primitiveM, isPrimitiveM := lease.(primitive.M); isPrimitiveM {
					// 转换 primitive.M 到 map[string]interface{}
					leaseMap = make(map[string]interface{})
					for k, v := range primitiveM {
						leaseMap[k] = v
					}
					ok = true
				} else if mapInterface, isMapInterface := lease.(map[string]interface{}); isMapInterface {
					leaseMap = mapInterface
					ok = true
				}

				if ok {
					// Metro2 log中的lease没有usrId，需要通过leaseId查询leases集合
					if leaseId, ok := leaseMap["leaseId"].(string); ok {
						fmt.Printf("  处理lease %d: %s\n", i+1, leaseId)
						leaseUsrId, err := getLandlordIdByLeaseId(ctx, leaseId)
						if err != nil {
							fmt.Printf("  警告：无法获取lease %s的landlord ID: %v\n", leaseId, err)
							continue
						}
						fmt.Printf("  lease %s的landlord ID: %s (目标: %s)\n", leaseId, leaseUsrId, landlordID)
						if leaseUsrId == landlordID {
							landlordUsageCount++
							fmt.Printf("  ✅ 找到landlord %s的lease: %s\n", landlordID, leaseId)
						}
					} else {
						fmt.Printf("  警告：lease %d没有leaseId字段\n", i+1)
					}
				} else {
					fmt.Printf("  警告：lease %d类型不支持，类型为: %T\n", i+1, lease)
				}
			}
		} else if interfaceSlice, ok := processedLeases.([]interface{}); ok {
			// 备用：尝试 []interface{} 类型
			fmt.Printf("  processedLeases数组长度: %d ([]interface{})\n", len(interfaceSlice))
			for i, lease := range interfaceSlice {
				if leaseMap, ok := lease.(map[string]interface{}); ok {
					if leaseId, ok := leaseMap["leaseId"].(string); ok {
						fmt.Printf("  处理lease %d: %s\n", i+1, leaseId)
						leaseUsrId, err := getLandlordIdByLeaseId(ctx, leaseId)
						if err != nil {
							fmt.Printf("  警告：无法获取lease %s的landlord ID: %v\n", leaseId, err)
							continue
						}
						fmt.Printf("  lease %s的landlord ID: %s (目标: %s)\n", leaseId, leaseUsrId, landlordID)
						if leaseUsrId == landlordID {
							landlordUsageCount++
							fmt.Printf("  ✅ 找到landlord %s的lease: %s\n", landlordID, leaseId)
						}
					} else {
						fmt.Printf("  警告：lease %d没有leaseId字段\n", i+1)
					}
				} else {
					fmt.Printf("  警告：lease %d不是map类型\n", i+1)
				}
			}
		} else {
			fmt.Printf("  警告：processedLeases类型不支持，类型为: %T\n", processedLeases)
		}
	}

	return landlordUsageCount, nil
}

// getLandlordIdByLeaseId 通过leaseId获取landlord ID
func getLandlordIdByLeaseId(ctx context.Context, leaseId string) (string, error) {
	leasesColl := gomongo.Coll("rr", "leases")
	if leasesColl == nil {
		return "", fmt.Errorf("leases collection not initialized")
	}

	var lease map[string]interface{}
	err := leasesColl.FindOne(ctx, map[string]interface{}{
		"_id": leaseId,
	}).Decode(&lease)

	if err != nil {
		return "", fmt.Errorf("failed to find lease %s: %v", leaseId, err)
	}

	if usrId, ok := lease["usrId"].(string); ok {
		return usrId, nil
	}

	return "", fmt.Errorf("lease %s has no usrId field", leaseId)
}

// checkAllMetro2Records 检查数据库中的所有Metro2记录
func checkAllMetro2Records(ctx context.Context) error {
	metro2LogsColl := gomongo.Coll("rr", "metro2_generation_logs")
	if metro2LogsColl == nil {
		return fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	// 查找所有记录
	cursor, err := metro2LogsColl.Find(ctx, map[string]interface{}{})
	if err != nil {
		return fmt.Errorf("failed to find Metro2 logs: %v", err)
	}
	defer cursor.Close(ctx)

	fmt.Printf("📋 数据库中的所有Metro2记录:\n")
	count := 0
	for cursor.Next(ctx) {
		var record map[string]interface{}
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		count++
		fmt.Printf("  记录 %d:\n", count)
		fmt.Printf("    _id: %v\n", record["_id"])
		fmt.Printf("    usrId: %v\n", record["usrId"])
		fmt.Printf("    reportMonth: %v\n", record["reportMonth"])
		fmt.Printf("    totalLeases: %v\n", record["totalLeases"])
		fmt.Printf("    processedLeases count: %v\n", getArrayLength(record["processedLeases"]))
		fmt.Printf("    fileName: %v\n", record["fileName"])
		fmt.Printf("    generatedAt: %v\n", record["generatedAt"])
		fmt.Println()
	}

	if count == 0 {
		fmt.Printf("❌ 数据库中没有任何Metro2记录\n")
	} else {
		fmt.Printf("✅ 找到 %d 条记录\n", count)
	}

	return nil
}

// getArrayLength 获取数组长度
func getArrayLength(arr interface{}) int {
	if arr == nil {
		return 0
	}
	if arrSlice, ok := arr.([]interface{}); ok {
		return len(arrSlice)
	}
	// 尝试其他可能的类型
	if arrSlice, ok := arr.([]map[string]interface{}); ok {
		return len(arrSlice)
	}
	return 0
}

// checkMetro2Records 检查数据库中的Metro2记录
func checkMetro2Records(ctx context.Context, userID string, year, month int) error {
	metro2LogsColl := gomongo.Coll("rr", "metro2_generation_logs")
	if metro2LogsColl == nil {
		return fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	reportMonth := fmt.Sprintf("%d-%02d", year, month)

	// 查找所有相关记录
	cursor, err := metro2LogsColl.Find(ctx, map[string]interface{}{
		"reportMonth": reportMonth,
	})
	if err != nil {
		return fmt.Errorf("failed to find Metro2 logs: %v", err)
	}
	defer cursor.Close(ctx)

	fmt.Printf("📋 数据库中 reportMonth='%s' 的所有记录:\n", reportMonth)
	count := 0
	for cursor.Next(ctx) {
		var record map[string]interface{}
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		count++
		fmt.Printf("  记录 %d:\n", count)
		fmt.Printf("    usrId: %v\n", record["usrId"])
		fmt.Printf("    reportMonth: %v\n", record["reportMonth"])
		fmt.Printf("    totalLeases: %v\n", record["totalLeases"])
		fmt.Printf("    processedLeases: %v\n", record["processedLeases"])
		fmt.Printf("    fileName: %v\n", record["fileName"])
	}

	if count == 0 {
		fmt.Printf("❌ 没有找到 reportMonth='%s' 的记录\n", reportMonth)
	} else {
		fmt.Printf("✅ 找到 %d 条记录\n", count)
	}

	// 特别检查目标用户的记录
	targetCount, err := metro2LogsColl.CountDocuments(ctx, map[string]interface{}{
		"usrId":       userID,
		"reportMonth": reportMonth,
	})
	if err != nil {
		return fmt.Errorf("failed to count target user records: %v", err)
	}

	fmt.Printf("🎯 用户 %s 的记录数量: %d\n", userID, targetCount)

	return nil
}

// getBaseQuota 获取基础配额
func getBaseQuota() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 3 // 默认值：3个租约
}
