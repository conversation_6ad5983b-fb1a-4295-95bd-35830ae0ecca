package main

import (
	"context"
	"fmt"
	"rent_report/entities"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("Failed to initialize environment: %v\n", err)
		return
	}

	ctx := context.Background()
	
	fmt.Println("=== NanoID使用量记录测试 ===")
	
	testUserID := "test_nanoid_user"
	
	fmt.Printf("测试用户: %s\n", testUserID)

	// 1. 创建使用量记录
	fmt.Println("\n1. 创建使用量记录")
	usage, err := entities.GetOrCreateUsageRecord(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error creating usage record: %v\n", err)
		return
	}
	
	fmt.Printf("记录ID: %s\n", usage.ID)
	fmt.Printf("用户ID: %s\n", usage.UID)
	fmt.Printf("年月: %d-%02d\n", usage.Year, usage.Month)
	fmt.Printf("基础配额: %d\n", usage.BaseQuota)
	
	// 验证ID格式
	if len(usage.ID) > 10 && usage.ID[:6] != "usage_" {
		fmt.Printf("✓ ID使用nanoId格式: %s\n", usage.ID)
	} else {
		fmt.Printf("✗ ID仍使用旧格式: %s\n", usage.ID)
	}

	// 2. 再次获取相同记录
	fmt.Println("\n2. 再次获取相同记录")
	usage2, err := entities.GetOrCreateUsageRecord(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error getting usage record: %v\n", err)
		return
	}
	
	if usage.ID == usage2.ID {
		fmt.Printf("✓ 获取到相同记录: %s\n", usage2.ID)
	} else {
		fmt.Printf("✗ 获取到不同记录: %s vs %s\n", usage.ID, usage2.ID)
	}

	// 3. 测试从Metro2日志计算使用量
	fmt.Println("\n3. 测试从Metro2日志计算使用量")
	stats, err := entities.GetUsageStatisticsFromMetro2(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error getting Metro2 statistics: %v\n", err)
		return
	}
	
	fmt.Printf("基础配额: %d\n", stats.BaseQuota)
	fmt.Printf("已使用: %d\n", stats.UsedCount)
	fmt.Printf("超量数量: %d\n", stats.OverageCount)
	fmt.Printf("剩余配额: %d\n", stats.RemainingQuota)
	fmt.Printf("超量费率: $%.2f %s/个\n", stats.OverageRate, stats.Currency)

	// 4. 测试使用量历史
	fmt.Println("\n4. 测试使用量历史")
	history, err := entities.GetUsageHistory(ctx, testUserID, 3)
	if err != nil {
		fmt.Printf("Error getting usage history: %v\n", err)
		return
	}
	
	fmt.Printf("历史记录数量: %d\n", len(history))
	for i, record := range history {
		fmt.Printf("  记录 %d: ID=%s, %d-%02d, 已用=%d\n", 
			i+1, record.ID, record.Year, record.Month, record.UsedCount)
	}

	// 5. 测试不同月份的记录
	fmt.Println("\n5. 测试不同月份的记录")
	// 获取上个月的记录（如果存在）
	usage3, err := entities.GetUsageRecord(ctx, testUserID, 2025, 7) // 7月
	if err != nil {
		fmt.Printf("Error getting July record: %v\n", err)
	} else if usage3 == nil {
		fmt.Printf("7月记录不存在（正常）\n")
	} else {
		fmt.Printf("7月记录: ID=%s, 已用=%d\n", usage3.ID, usage3.UsedCount)
	}

	// 6. 验证数据库中的记录格式
	fmt.Println("\n6. 验证数据库中的记录格式")
	coll := gomongo.Coll("rr", "rent_report_usage")
	if coll != nil {
		count, err := coll.CountDocuments(ctx, map[string]interface{}{"uid": testUserID})
		if err != nil {
			fmt.Printf("Error counting records: %v\n", err)
		} else {
			fmt.Printf("用户 %s 的记录总数: %d\n", testUserID, count)
		}
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n验证要点:")
	fmt.Println("1. ✓ 记录ID使用nanoId格式")
	fmt.Println("2. ✓ 查询使用uid+year+month组合")
	fmt.Println("3. ✓ 支持从Metro2日志计算使用量")
	fmt.Println("4. ✓ 历史记录查询正常")
}

func initializeEnvironment() error {
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	return nil
}
