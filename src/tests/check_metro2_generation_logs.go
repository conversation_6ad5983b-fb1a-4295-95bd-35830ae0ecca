package main

import (
	"context"
	"fmt"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to init MongoDB: %v\n", err)
		return
	}

	ctx := context.Background()
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		fmt.Println("metro2_generation_logs collection not initialized")
		return
	}

	// 查询最近的Metro2生成记录，按时间倒序
	cursor, err := coll.Find(ctx, bson.M{}, &options.FindOptions{
		Sort: bson.M{"generatedAt": -1},
	})
	if err != nil {
		fmt.Printf("Failed to query: %v\n", err)
		return
	}
	defer cursor.Close(ctx)

	var logs []bson.M
	if err = cursor.All(ctx, &logs); err != nil {
		fmt.Printf("Failed to decode: %v\n", err)
		return
	}

	fmt.Printf("=== Metro2 Generation Logs ===\n")
	fmt.Printf("Found %d Metro2 generation logs:\n\n", len(logs))

	for i, log := range logs {
		fmt.Printf("Log %d:\n", i+1)
		fmt.Printf("  ID: %v\n", log["_id"])
		fmt.Printf("  ReportMonth: %v\n", log["reportMonth"])
		fmt.Printf("  UserID: %v\n", log["usrId"])
		fmt.Printf("  GeneratedAt: %v\n", log["generatedAt"])
		if processedLeases, ok := log["processedLeases"]; ok {
			if leases, ok := processedLeases.(bson.A); ok {
				fmt.Printf("  ProcessedLeases count: %d\n", len(leases))

				// 显示前几个lease的详情
				for j, lease := range leases {
					if j >= 3 { // 只显示前3个
						fmt.Printf("    ... and %d more leases\n", len(leases)-3)
						break
					}
					if leaseMap, ok := lease.(bson.M); ok {
						fmt.Printf("    Lease %d: ID=%v, UserID=%v\n", j+1,
							leaseMap["leaseId"], leaseMap["usrId"])
					}
				}
			}
		}
		fmt.Println()
	}
}
