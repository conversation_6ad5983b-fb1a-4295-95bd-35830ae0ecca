package main

import (
	"context"
	"fmt"
	"rent_report/entities"
	"rent_report/services"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/sub"
	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	// 初始化配置和数据库
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	if err := golog.InitLog(); err != nil {
		fmt.Printf("Failed to initialize logging: %v\n", err)
		return
	}

	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		}
	}

	ctx := context.Background()

	// 从订阅记录中获取的实际用户ID
	testUserID := "6RDHxuNP6pT"

	fmt.Printf("=== 调试用户订阅状态 ===\n")
	fmt.Printf("用户ID: %s\n\n", testUserID)

	// 1. 检查用户基本信息
	fmt.Println("1. 检查用户基本信息")
	user, err := entities.GetUserByID(ctx, testUserID)
	if err != nil {
		fmt.Printf("❌ 获取用户信息失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 用户邮箱: %s\n", user.Email)
	fmt.Printf("✅ 账户类型: %s\n", user.AccountType)

	// 2. 检查用户的所有订阅
	fmt.Println("\n2. 检查用户的所有订阅")
	userSubColl := gomongo.Coll("rr", "usersub")
	cursor, err := userSubColl.Find(ctx, bson.M{"uid": testUserID})
	if err != nil {
		fmt.Printf("❌ 查询订阅失败: %v\n", err)
		return
	}
	defer cursor.Close(ctx)

	var allSubs []bson.M
	if err = cursor.All(ctx, &allSubs); err != nil {
		fmt.Printf("❌ 解析订阅数据失败: %v\n", err)
		return
	}

	fmt.Printf("找到 %d 个订阅记录:\n", len(allSubs))
	for i, sub := range allSubs {
		fmt.Printf("  订阅 %d:\n", i+1)
		fmt.Printf("    ID: %v\n", sub["_id"])
		fmt.Printf("    planId: %v\n", sub["planId"])
		fmt.Printf("    stripeSubId: %v\n", sub["stripeSubId"])
		fmt.Printf("    status: %v\n", sub["sts"])
		fmt.Printf("    prdId: %v\n", sub["prdId"])
		fmt.Printf("    创建时间: %v\n", sub["ts"])
		fmt.Printf("\n")
	}

	// 3. 特别检查extra_report订阅
	fmt.Println("3. 检查extra_report订阅")
	var extraReportSub bson.M
	err = userSubColl.FindOne(ctx, bson.M{
		"uid":    testUserID,
		"planId": "extra_report_plan",
		"sts":    "active",
	}).Decode(&extraReportSub)

	if err != nil {
		fmt.Printf("❌ 没有找到active的extra_report订阅: %v\n", err)

		// 检查是否有非active的extra_report订阅
		err2 := userSubColl.FindOne(ctx, bson.M{
			"uid":    testUserID,
			"planId": "extra_report_plan",
		}).Decode(&extraReportSub)

		if err2 != nil {
			fmt.Printf("❌ 完全没有extra_report订阅记录\n")
		} else {
			fmt.Printf("⚠️  找到非active的extra_report订阅:\n")
			fmt.Printf("    status: %v\n", extraReportSub["sts"])
			fmt.Printf("    stripeSubId: %v\n", extraReportSub["stripeSubId"])
		}
	} else {
		fmt.Printf("✅ 找到active的extra_report订阅:\n")
		fmt.Printf("    stripeSubId: %v\n", extraReportSub["stripeSubId"])
	}

	// 4. 计算用户的使用量
	fmt.Println("\n4. 计算用户使用量")

	// 检查当前月份（2025-08）的使用量
	year := 2025
	month := 8
	fmt.Printf("计算月份: %d-%02d\n", year, month)

	usageCount, err := entities.CalculateUsageFromMetro2Logs(ctx, testUserID, year, month)
	if err != nil {
		fmt.Printf("❌ 计算使用量失败: %v\n", err)
		return
	}

	baseQuota := getBaseQuota()
	overageCount := 0
	if usageCount > baseQuota {
		overageCount = usageCount - baseQuota
	}

	fmt.Printf("✅ 使用量统计:\n")
	fmt.Printf("    基础配额: %d\n", baseQuota)
	fmt.Printf("    实际使用: %d\n", usageCount)
	fmt.Printf("    超量数量: %d\n", overageCount)

	// 5. 测试向Stripe报告使用量
	if overageCount > 0 {
		fmt.Println("\n5. 测试向Stripe报告使用量")
		usageService := services.NewMonthlyUsageReportService()
		err = usageService.ReportSingleUserUsage(ctx, testUserID, overageCount, year, month)
		if err != nil {
			fmt.Printf("❌ 向Stripe报告使用量失败: %v\n", err)
		} else {
			fmt.Printf("✅ 成功向Stripe报告使用量: %d个超量\n", overageCount)
		}
	} else {
		fmt.Println("\n5. 无超量使用，无需向Stripe报告")
	}

	// 6. 检查Stripe中的订阅详情
	fmt.Println("\n6. 检查Stripe中的订阅详情")
	err = checkStripeSubscriptionDetails(extraReportSub["stripeSubId"].(string))
	if err != nil {
		fmt.Printf("❌ 检查Stripe订阅详情失败: %v\n", err)
	}

	// 7. 检查旧订阅的详情（8月份可能应该计入旧订阅）
	fmt.Println("\n7. 检查旧订阅详情")
	err = checkStripeSubscriptionDetails("sub_1Rv1X3RdRW2qyPyr6E2b76iL")
	if err != nil {
		fmt.Printf("❌ 检查旧订阅详情失败: %v\n", err)
	}
}

// checkStripeSubscriptionDetails 检查Stripe订阅详情
func checkStripeSubscriptionDetails(stripeSubId string) error {
	secretKey := goconfig.Config("stripe.secret_key")
	if secretKey == nil {
		return fmt.Errorf("Stripe密钥未配置")
	}
	stripe.Key = secretKey.(string)

	// 获取订阅详情
	subscription, err := sub.Get(stripeSubId, nil)
	if err != nil {
		return fmt.Errorf("获取订阅失败: %v", err)
	}

	fmt.Printf("✅ Stripe订阅详情:\n")
	fmt.Printf("    订阅ID: %s\n", subscription.ID)
	fmt.Printf("    状态: %s\n", subscription.Status)
	fmt.Printf("    客户ID: %s\n", subscription.Customer.ID)
	fmt.Printf("    当前周期开始: %s\n", time.Unix(subscription.CurrentPeriodStart, 0).Format("2006-01-02 15:04:05"))
	fmt.Printf("    当前周期结束: %s\n", time.Unix(subscription.CurrentPeriodEnd, 0).Format("2006-01-02 15:04:05"))

	// 检查订阅项目
	fmt.Printf("    订阅项目数量: %d\n", len(subscription.Items.Data))
	for i, item := range subscription.Items.Data {
		fmt.Printf("    项目 %d:\n", i+1)
		fmt.Printf("      ID: %s\n", item.ID)
		fmt.Printf("      数量: %d\n", item.Quantity)
	}

	return nil
}

// getBaseQuota 获取基础配额
func getBaseQuota() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 3 // 默认值
}
