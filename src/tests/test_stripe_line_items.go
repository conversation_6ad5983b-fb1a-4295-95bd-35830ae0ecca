package main

import (
	"fmt"

	"github.com/real-rm/goconfig"
	"github.com/stripe/stripe-go/v75"
)

// getExtraReportPriceId 从配置文件获取extra_report价格ID
func getExtraReportPriceId() string {
	if priceId := goconfig.Config("stripe.extra_report_price_id"); priceId != nil {
		if priceIdStr, ok := priceId.(string); ok && priceIdStr != "" {
			return priceIdStr
		}
	}
	return "price_1RuwwtRdRW2qyPyrLm0nLLG0" // 默认值
}

func main() {
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		}
	}

	fmt.Println("=== 测试Stripe Line Items配置 ===")

	// 模拟checkout session的line items配置
	finalPriceId := "price_1Rv0DfRdRW2qyPyrPAl8K7m2" // 基础订阅价格
	extraReportPriceId := getExtraReportPriceId()

	fmt.Printf("基础订阅价格ID: %s\n", finalPriceId)
	fmt.Printf("Extra Report价格ID: %s\n", extraReportPriceId)

	// 准备line items：基础订阅 + extra_report metered billing
	lineItems := []*stripe.CheckoutSessionLineItemParams{
		{
			Price:    stripe.String(finalPriceId),
			Quantity: stripe.Int64(1),
		},
		{
			// 添加extra_report的metered价格 - metered类型不能设置quantity
			Price: stripe.String(extraReportPriceId),
		},
	}

	fmt.Println("\n📋 Line Items配置:")
	for i, item := range lineItems {
		fmt.Printf("  Item %d:\n", i+1)
		if item.Price != nil {
			fmt.Printf("    Price: %s\n", *item.Price)
		}
		if item.Quantity != nil {
			fmt.Printf("    Quantity: %d\n", *item.Quantity)
		} else {
			fmt.Printf("    Quantity: 未设置 (metered类型)\n")
		}
	}

	// 测试创建checkout session参数（不实际调用Stripe API）
	params := &stripe.CheckoutSessionParams{
		PaymentMethodTypes:       stripe.StringSlice([]string{"card"}),
		Mode:                     stripe.String(string(stripe.CheckoutSessionModeSubscription)),
		BillingAddressCollection: stripe.String("required"),
		LineItems:                lineItems,
		SuccessURL:               stripe.String("http://localhost:8089/success"),
		CancelURL:                stripe.String("http://localhost:8089/cancel"),
		Metadata: map[string]string{
			"uid":                  "6RDHxuNP6pT",
			"prdId":                "prod_SqeFaz9P1fDdOq",
			"extraReportProductId": "prod_SqeFaz9P1fDdOq",
		},
	}

	fmt.Println("\n✅ Checkout Session参数配置完成")
	fmt.Printf("模式: %s\n", *params.Mode)
	fmt.Printf("支付方式: %v\n", params.PaymentMethodTypes)
	fmt.Printf("Line Items数量: %d\n", len(params.LineItems))

	// 验证配置
	fmt.Println("\n🔍 配置验证:")

	// 检查第一个item（基础订阅）
	if len(params.LineItems) > 0 {
		item1 := params.LineItems[0]
		if item1.Quantity != nil && *item1.Quantity == 1 {
			fmt.Println("✅ 基础订阅配置正确：有quantity=1")
		} else {
			fmt.Println("❌ 基础订阅配置错误：缺少quantity")
		}
	}

	// 检查第二个item（metered订阅）
	if len(params.LineItems) > 1 {
		item2 := params.LineItems[1]
		if item2.Quantity == nil {
			fmt.Println("✅ Metered订阅配置正确：没有设置quantity")
		} else {
			fmt.Println("❌ Metered订阅配置错误：不应该设置quantity")
		}
	}

	fmt.Println("\n🎉 测试完成！")
	fmt.Println("现在可以尝试在网页上点击'Confirm and Pay'按钮测试实际的订阅创建。")
}
