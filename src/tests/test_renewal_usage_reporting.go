package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"rent_report/entities"
	"rent_report/services"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("Failed to initialize environment: %v\n", err)
		return
	}

	ctx := context.Background()

	fmt.Println("=== 续费时使用量报告测试 ===")

	// 使用实际存在的用户ID进行测试
	testUserID := "6RDHxuNP6pT" // 从Metro2记录中找到的实际有租约的用户ID

	fmt.Printf("测试用户: %s\n", testUserID)

	// 显示当前时间信息
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)

	fmt.Printf("当前时间: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Printf("当前年月: %d-%02d\n", now.Year(), int(now.Month()))
	fmt.Printf("上个月年月: %d-%02d (Metro2查找目标)\n", lastMonth.Year(), int(lastMonth.Month()))

	// 1. 测试使用量计算
	fmt.Println("\n1. 测试使用量计算")
	usageCount, err := entities.CalculateUsageFromMetro2Logs(ctx, testUserID, lastMonth.Year(), int(lastMonth.Month()))
	if err != nil {
		fmt.Printf("Error calculating usage: %v\n", err)
		return
	}

	fmt.Printf("从Metro2日志计算的使用量: %d\n", usageCount)

	// 2. 获取基础配额
	baseQuota := getBaseQuota()
	fmt.Printf("基础配额: %d\n", baseQuota)

	// 3. 计算超量
	if usageCount > baseQuota {
		overageCount := usageCount - baseQuota
		fmt.Printf("超量数量: %d\n", overageCount)

		// 4. 测试使用量报告服务
		fmt.Println("\n2. 测试使用量报告服务")
		usageService := services.NewMonthlyUsageReportService()

		// 模拟续费时的使用量报告
		err = usageService.ReportSingleUserUsage(ctx, testUserID, overageCount, lastMonth.Year(), int(lastMonth.Month()))
		if err != nil {
			fmt.Printf("Error reporting usage to Stripe: %v\n", err)
			// 这里可能会失败，因为用户可能没有extra_report订阅，这是正常的
			fmt.Printf("注意: 如果用户没有extra_report订阅，这个错误是正常的\n")
		} else {
			fmt.Printf("✅ 成功向Stripe报告使用量: %d个超量\n", overageCount)
		}
	} else {
		fmt.Printf("✅ 使用量在配额内，无需报告超量: %d <= %d\n", usageCount, baseQuota)
	}

	// 5. 测试完整的续费处理逻辑
	fmt.Println("\n3. 测试完整的续费处理逻辑")
	err = processUsageReportingForRenewalTest(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error in renewal processing: %v\n", err)
	} else {
		fmt.Printf("✅ 续费处理逻辑测试完成\n")
	}

	fmt.Println("\n=== 测试完成 ===")
}

// processUsageReportingForRenewalTest 测试续费处理逻辑
func processUsageReportingForRenewalTest(ctx context.Context, userID string) error {
	fmt.Printf("模拟续费处理 - 用户ID: %s\n", userID)

	// 检查用户是否有extra_report订阅
	userSubColl := gomongo.Coll("rr", "usersub")
	var extraReportSub map[string]interface{}
	err := userSubColl.FindOne(ctx, map[string]interface{}{
		"uid":    userID,
		"planId": "extra_report_plan",
		"sts":    "active",
	}).Decode(&extraReportSub)

	if err != nil {
		fmt.Printf("ℹ️  用户没有extra_report订阅，跳过使用量报告\n")
		return nil
	}

	fmt.Printf("✅ 找到extra_report订阅\n")

	// 获取上个月的年月
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)
	year := lastMonth.Year()
	month := int(lastMonth.Month())

	// 从Metro2日志计算使用量
	usageCount, err := entities.CalculateUsageFromMetro2Logs(ctx, userID, year, month)
	if err != nil {
		return fmt.Errorf("failed to calculate usage: %v", err)
	}

	baseQuota := getBaseQuota()

	fmt.Printf("使用量计算结果: %d (配额: %d)\n", usageCount, baseQuota)

	if usageCount <= baseQuota {
		fmt.Printf("✅ 无超量使用，跳过Stripe报告\n")
		return nil
	}

	overageCount := usageCount - baseQuota
	fmt.Printf("⚠️  检测到超量使用: %d个\n", overageCount)

	// 创建使用量报告服务
	usageService := services.NewMonthlyUsageReportService()
	err = usageService.ReportSingleUserUsage(ctx, userID, overageCount, year, month)
	if err != nil {
		return fmt.Errorf("failed to report to Stripe: %v", err)
	}

	fmt.Printf("✅ 成功向Stripe报告超量使用\n")
	return nil
}

// getBaseQuota 获取基础配额
func getBaseQuota() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 3 // 默认值
}

// initializeEnvironment 初始化环境
func initializeEnvironment() error {
	// Set default config file if none specified
	if os.Getenv("RMBASE_FILE_CFG") == "" {
		os.Setenv("RMBASE_FILE_CFG", "../configs/local.ini")
	}

	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to init log: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		return fmt.Errorf("failed to init MongoDB: %v", err)
	}

	return nil
}
