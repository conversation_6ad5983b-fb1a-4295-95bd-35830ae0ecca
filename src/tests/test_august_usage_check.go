package main

import (
	"context"
	"fmt"
	"rent_report/services"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/v75"
)

func main() {
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		} else {
			fmt.Printf("❌ Stripe密钥格式错误\n")
			return
		}
	} else {
		fmt.Printf("❌ 未找到Stripe密钥配置\n")
		return
	}

	ctx := context.Background()
	testUserID := "6RDHxuNP6pT"
	year := 2025
	month := 8 // 检查8月份

	fmt.Printf("=== 重新检查8月份使用量 ===\n")

	// 1. 检查8月份Metro2记录和使用量
	fmt.Printf("1. 检查8月份Metro2记录和使用量\n")
	usageService := services.NewMonthlyUsageReportService()

	// 获取所有用户的使用量（包括我们的测试用户）
	userUsageMap, err := usageService.CalculateAllUserUsageFromMetro2(ctx, year, month)
	if err != nil {
		fmt.Printf("❌ 获取8月份所有用户使用量失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 8月份总用户数: %d\n", len(userUsageMap))

	// 检查我们的测试用户
	if usage, exists := userUsageMap[testUserID]; exists {
		fmt.Printf("✅ 用户 %s 在8月份的使用量: %d 个lease\n", testUserID, usage)

		// 计算超量 - 从配置文件获取真实配额
		baseQuota := 3 // 配置文件中设置的值
		if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
			if quotaInt64, ok := quota.(int64); ok {
				baseQuota = int(quotaInt64)
			}
		}
		if usage > baseQuota {
			overageCount := usage - baseQuota
			fmt.Printf("🔥 超量使用: %d 个 (总使用: %d, 基础配额: %d)\n", overageCount, usage, baseQuota)
		} else {
			fmt.Printf("✅ 没有超量使用 (总使用: %d, 基础配额: %d)\n", usage, baseQuota)
		}
	} else {
		fmt.Printf("❌ 用户 %s 在8月份没有使用记录\n", testUserID)
	}

	// 2. 显示所有用户的使用情况
	fmt.Printf("\n2. 8月份所有用户使用情况:\n")
	baseQuota := 3 // 从配置获取
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt64, ok := quota.(int64); ok {
			baseQuota = int(quotaInt64)
		}
	}
	for userID, usage := range userUsageMap {
		if usage > baseQuota {
			overageCount := usage - baseQuota
			fmt.Printf("   用户 %s: %d 个lease (超量: %d)\n", userID, usage, overageCount)
		} else {
			fmt.Printf("   用户 %s: %d 个lease (无超量)\n", userID, usage)
		}
	}

	fmt.Printf("\n🤔 如果8月份确实有超量使用，那么问题可能是:\n")
	fmt.Printf("1. 超量使用还没有被上报到Stripe\n")
	fmt.Printf("2. 或者上报了但还没有在invoice中体现\n")
	fmt.Printf("3. 需要检查Stripe的usage records\n")
}
