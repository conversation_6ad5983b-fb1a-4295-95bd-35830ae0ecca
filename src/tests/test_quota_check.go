package main

import (
	"context"
	"fmt"
	"rent_report/services"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/v75"
)

func main() {
	// 初始化配置
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		return
	}

	// 初始化Stripe
	if stripeKey := goconfig.Config("stripe.secret_key"); stripeKey != nil {
		if keyStr, ok := stripeKey.(string); ok && keyStr != "" {
			stripe.Key = keyStr
			fmt.Printf("✅ Stripe密钥已初始化\n")
		} else {
			fmt.Printf("❌ Stripe密钥格式错误\n")
			return
		}
	} else {
		fmt.Printf("❌ 未找到Stripe密钥配置\n")
		return
	}

	ctx := context.Background()
	testUserID := "6RDHxuNP6pT"
	year := 2025
	month := 8

	fmt.Printf("=== 检查配额配置和计算 ===\n")

	// 1. 检查配置文件中的基础配额
	fmt.Printf("1. 检查配置文件中的基础配额\n")
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		fmt.Printf("✅ 配置文件中的baseQuota: %v (类型: %T)\n", quota, quota)
		
		if quotaInt, ok := quota.(int); ok {
			fmt.Printf("✅ 转换为int: %d\n", quotaInt)
		} else if quotaInt64, ok := quota.(int64); ok {
			fmt.Printf("✅ 转换为int64: %d\n", quotaInt64)
		} else {
			fmt.Printf("❌ 无法转换为数字类型\n")
		}
	} else {
		fmt.Printf("❌ 未找到rentReportUsage.baseQuota配置\n")
	}

	// 2. 使用服务计算超量
	fmt.Printf("\n2. 使用服务计算8月份超量使用\n")
	usageService := services.NewMonthlyUsageReportService()
	overageCount, err := usageService.CalculateUserUsage(ctx, testUserID, year, month)
	if err != nil {
		fmt.Printf("❌ 计算超量失败: %v\n", err)
	} else {
		fmt.Printf("✅ 用户 %s 在8月份的超量使用: %d 个\n", testUserID, overageCount)
	}

	// 3. 获取详细的使用情况
	fmt.Printf("\n3. 获取详细使用情况\n")
	userUsageMap, err := usageService.CalculateAllUserUsageFromMetro2(ctx, year, month)
	if err != nil {
		fmt.Printf("❌ 获取使用情况失败: %v\n", err)
	} else {
		if usage, exists := userUsageMap[testUserID]; exists {
			// 手动获取配额进行计算
			baseQuota := 20 // 默认值
			if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
				if quotaInt, ok := quota.(int); ok {
					baseQuota = quotaInt
				} else if quotaInt64, ok := quota.(int64); ok {
					baseQuota = int(quotaInt64)
				}
			}
			
			fmt.Printf("✅ 总使用量: %d 个lease\n", usage)
			fmt.Printf("✅ 基础配额: %d 个lease\n", baseQuota)
			if usage > baseQuota {
				overage := usage - baseQuota
				fmt.Printf("🔥 超量使用: %d 个 (需要额外付费)\n", overage)
			} else {
				fmt.Printf("✅ 没有超量使用\n")
			}
		}
	}

	fmt.Printf("\n=== 结论 ===\n")
	if overageCount > 0 {
		fmt.Printf("🔥 8月份有 %d 个超量使用，应该产生额外费用！\n", overageCount)
		fmt.Printf("💡 如果Stripe invoice中没有显示，可能需要手动上报\n")
	} else {
		fmt.Printf("✅ 8月份没有超量使用，不产生额外费用\n")
	}
}
