package entities

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"rent_report/utils"
	"strconv"
	"strings"
	"time"

	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// isProduction 检查是否为生产环境
func isProduction() bool {
	env := strings.ToLower(os.Getenv("GIN_MODE"))
	if env == "release" {
		return true
	}

	if strings.ToLower(os.Getenv("ENVIRONMENT")) == "production" {
		return true
	}

	return false
}

// maskSensitiveInfo 遮蔽敏感信息用于日志记录
func maskSensitiveInfo(info string) string {
	if len(info) <= 4 {
		return "****"
	}
	// 只显示前2位和后2位，中间用*替代
	return info[:2] + strings.Repeat("*", len(info)-4) + info[len(info)-2:]
}

// safeMetro2Log 安全的Metro2日志记录函数
func safeMetro2Log(level string, msg string, args ...interface{}) {
	// 在生产环境中，只记录Info级别及以上的日志
	if isProduction() && (level == "debug" || level == "trace") {
		return
	}

	// 过滤敏感信息
	filteredArgs := make([]interface{}, 0, len(args))
	for i := 0; i < len(args); i += 2 {
		if i+1 >= len(args) {
			break
		}

		key := args[i]
		value := args[i+1]

		// 检查是否为敏感字段
		if keyStr, ok := key.(string); ok {
			switch keyStr {
			case "sinNumber", "socialSecurityNumber":
				// SIN号码完全不记录
				continue
			case "phone", "telephoneNumber":
				// 电话号码遮蔽
				if phoneStr, ok := value.(string); ok {
					filteredArgs = append(filteredArgs, key, maskSensitiveInfo(phoneStr))
				} else {
					filteredArgs = append(filteredArgs, key, "****")
				}
			case "firstName", "lastName":
				// 姓名遮蔽（在生产环境中）
				if isProduction() {
					if nameStr, ok := value.(string); ok {
						filteredArgs = append(filteredArgs, key, maskSensitiveInfo(nameStr))
					} else {
						filteredArgs = append(filteredArgs, key, "****")
					}
				} else {
					filteredArgs = append(filteredArgs, key, value)
				}
			default:
				filteredArgs = append(filteredArgs, key, value)
			}
		} else {
			filteredArgs = append(filteredArgs, key, value)
		}
	}

	// 根据级别记录日志
	switch level {
	case "trace":
		// Trace级别的详细调试信息，只在开发环境记录
		if !isProduction() {
			golog.Debug("[TRACE] "+msg, filteredArgs...)
		}
	case "debug":
		golog.Debug(msg, filteredArgs...)
	case "info":
		golog.Info(msg, filteredArgs...)
	case "warn":
		golog.Warn(msg, filteredArgs...)
	case "error":
		golog.Error(msg, filteredArgs...)
	default:
		golog.Info(msg, filteredArgs...)
	}
}

// This struct represents generation records in the database. It does not store the report format itself due to sensitivity of the data.
type Metro2Report struct {
	ID             string                 `json:"id" bson:"_id"`
	UserID         string                 `json:"-" bson:"usrId"`
	OrganizationID string                 `json:"-" bson:"orgId,omitempty"`
	Metro2Data     map[string]interface{} `json:"metro2Data" bson:"metro2Data"`
}

// Metro2GenerationLog 记录Metro2文件生成的详细日志信息
type Metro2GenerationLog struct {
	ID              string             `json:"id" bson:"_id"`
	UserID          string             `json:"userId" bson:"usrId"`
	OrganizationID  string             `json:"-" bson:"orgId,omitempty"`
	ReportMonth     string             `json:"reportMonth" bson:"reportMonth"` // YYYY-MM format
	GeneratedAt     time.Time          `json:"generatedAt" bson:"generatedAt"`
	TotalLeases     int                `json:"totalLeases" bson:"totalLeases"`
	ProcessedLeases []LeaseProcessInfo `json:"processedLeases" bson:"processedLeases"`
	TotalTenants    int                `json:"totalTenants" bson:"totalTenants"`
	TotalPayments   int                `json:"totalPayments" bson:"totalPayments"`
	FileSize        int64              `json:"fileSize" bson:"fileSize"`
	FileName        string             `json:"fileName" bson:"fileName"`
	FileID          string             `json:"fileId,omitempty" bson:"fileId,omitempty"`                 // GridFS file ID (deprecated)
	JSONDataSize    int64              `json:"jsonDataSize" bson:"jsonDataSize"`                         // Size of JSON data
	JSONBackupPath  string             `json:"jsonBackupPath,omitempty" bson:"jsonBackupPath,omitempty"` // Path to JSON backup file
	Metro2FilePath  string             `json:"metro2FilePath,omitempty" bson:"metro2FilePath,omitempty"` // Path to Metro2 txt file
}

// LeaseProcessInfo 记录每个租约的处理信息
type LeaseProcessInfo struct {
	LeaseID         string   `json:"leaseId" bson:"leaseId"`
	PropertyName    string   `json:"propertyName" bson:"propertyName"`
	PropertyAddress string   `json:"propertyAddress" bson:"propertyAddress"`
	TenantCount     int      `json:"tenantCount" bson:"tenantCount"`
	TenantNames     []string `json:"tenantNames" bson:"tenantNames"`
	PaymentCount    int      `json:"paymentCount" bson:"paymentCount"`
	RentAmount      float64  `json:"rentAmount" bson:"rentAmount"`
	CurrentBalance  float64  `json:"currentBalance" bson:"currentBalance"`
	AccountStatus   string   `json:"accountStatus" bson:"accountStatus"`
	StartDate       string   `json:"startDate" bson:"startDate"`
	EndDate         string   `json:"endDate,omitempty" bson:"endDate,omitempty"`
}

func (m *Metro2Report) Create(ctx context.Context) error {
	var reportColl = gomongo.Coll("rr", "metro2_reports")
	if reportColl == nil {
		return fmt.Errorf("metro2_reports collection not initialized")
	}

	scope, err := GetResourceScope(ctx, m.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	m.OrganizationID = scope.OrganizationID

	// Validate required fields
	if m.ID == "" {
		return fmt.Errorf("id")
	}

	_, err = reportColl.InsertOne(ctx, m)
	if err != nil {
		return fmt.Errorf("failed to insert metro2 report: %v", err)
	}

	return nil
}

func GetMetro2Reports(ctx context.Context, reportDate time.Time, userID string) ([]*Metro2Report, error) {
	var reportColl = gomongo.Coll("rr", "metro2_reports")
	if reportColl == nil {
		return nil, fmt.Errorf("metro2_reports collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := bson.M{
		"usrId": userID,
		"orgId": scope.OrganizationID,
	}

	// If reportDate is provided, filter by the month
	if !reportDate.IsZero() {
		startOfMonth := time.Date(reportDate.Year(), reportDate.Month(), 1, 0, 0, 0, 0, reportDate.Location())
		endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)
		filter["lastReportDt"] = bson.M{
			"$gte": startOfMonth,
			"$lte": endOfMonth,
		}
	}

	cursor, err := reportColl.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find metro2 reports: %v", err)
	}
	defer cursor.Close(ctx)

	// Initialize reports as empty slice instead of nil
	reports := make([]*Metro2Report, 0)
	if err = cursor.All(ctx, &reports); err != nil {
		return nil, fmt.Errorf("failed to decode metro2 reports: %v", err)
	}

	return reports, nil
}

// Metro2GenerationLog CRUD methods

// Create 创建Metro2生成日志记录
func (m *Metro2GenerationLog) Create(ctx context.Context) error {
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		return fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	scope, err := GetResourceScope(ctx, m.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	m.OrganizationID = scope.OrganizationID
	m.GeneratedAt = time.Now()

	if m.ID == "" {
		m.ID = utils.GenerateNanoID()
	}

	_, err = coll.InsertOne(ctx, m)
	if err != nil {
		return fmt.Errorf("failed to insert metro2 generation log: %v", err)
	}

	return nil
}

// GetMetro2GenerationLogs 获取Metro2生成日志列表
func GetMetro2GenerationLogs(ctx context.Context, userID string, limit int64) ([]*Metro2GenerationLog, error) {
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		return nil, fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := bson.M{
		"usrId": userID,
		"orgId": scope.OrganizationID,
	}

	cursor, err := coll.Find(ctx, filter, nil, 0, limit, "-generatedAt") // Sort by generatedAt desc
	if err != nil {
		return nil, fmt.Errorf("failed to find metro2 generation logs: %v", err)
	}
	defer cursor.Close(ctx)

	logs := make([]*Metro2GenerationLog, 0)
	if err = cursor.All(ctx, &logs); err != nil {
		return nil, fmt.Errorf("failed to decode metro2 generation logs: %v", err)
	}

	return logs, nil
}

// GetMetro2GenerationLogByID 根据ID获取Metro2生成日志
func GetMetro2GenerationLogByID(ctx context.Context, logID string) (*Metro2GenerationLog, error) {
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		return nil, fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	var log Metro2GenerationLog
	err := coll.FindOne(ctx, bson.M{"_id": logID}).Decode(&log)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("metro2 generation log not found")
		}
		return nil, fmt.Errorf("failed to get metro2 generation log: %v", err)
	}

	return &log, nil
}

func GetPayments(ctx context.Context, leaseID string, userID string) ([]*TenantPayment, int64, error) {
	payments, total, err := GetTenantPayments(ctx, leaseID, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenant payments: %v", err)
	}

	var result []*TenantPayment
	for i := range payments {
		result = append(result, &payments[i])
	}

	return result, total, nil
}

// GetPaymentsUpToDate 获取指定日期及之前的payment记录，用于Metro2报告生成
func GetPaymentsUpToDate(ctx context.Context, leaseID string, userID string, endDate time.Time) ([]*TenantPayment, int64, error) {
	payments, total, err := GetTenantPaymentsUpToDate(ctx, leaseID, userID, endDate)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenant payments up to date: %v", err)
	}

	var result []*TenantPayment
	for i := range payments {
		result = append(result, &payments[i])
	}

	return result, total, nil
}

// GenerateMetro2FileData generates the Metro2 file data in JSON format for multiple leases
// Returns JSON data and generation log
func GenerateMetro2FileData(ctx context.Context, reportMonth time.Time, userID string) ([]byte, *Metro2GenerationLog, error) {
	// Calculate the start and end of the month
	startOfMonth := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, -1) // Last day of the month

	// Initialize generation log
	generationLog := &Metro2GenerationLog{
		UserID:          userID,
		ReportMonth:     reportMonth.Format("2006-01"),
		ProcessedLeases: make([]LeaseProcessInfo, 0),
	}

	safeMetro2Log("info", "Metro2 Database Query Info",
		"reportMonth", reportMonth.Format("2006-01"),
		"monthStart", startOfMonth.Format("2006-01-02"),
		"monthEnd", endOfMonth.Format("2006-01-02"))

	// Get current user info for permission check
	user, err := GetUserByID(ctx, userID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get user info: %v", err)
	}

	// Get all active leases for the given month, only userID is admin can get
	leases, err := GetLeasesForMonth(ctx, reportMonth, userID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get leases: %v", err)
	}
	if len(leases) == 0 {
		return nil, nil, fmt.Errorf("no leases found for the given month")
	}

	safeMetro2Log("info", "Found leases for Metro2 report", "count", len(leases))

	// Update generation log with basic info
	generationLog.TotalLeases = len(leases)

	// Create Metro2 data structure with header
	metro2Data := map[string]interface{}{
		"header": map[string]interface{}{
			"recordDescriptorWord":     calculateRDW(leases),
			"recordIdentifier":         "HEADER",
			"EquifaxProgramIdentifier": "1101653", // fixed value
			"activityDate":             endOfMonth.Format("2006-01-02T00:00:00Z"),
			"dateCreated":              time.Now().Format("2006-01-02T00:00:00Z"),
			"programDate":              time.Now().Format("2006-01-02T00:00:00Z"),
			"programRevisionDate":      time.Now().Format("2006-01-02T00:00:00Z"),
			"reporterName":             "REALMASTER",                                           // fixed value
			"reporterAddress":          "50 Acadia Ave #130, Markham, Ontario, Canada L3R 5Z2", // fixed value
			"reporterTelephoneNumber":  6475185728,                                             // fixed value
		},
		"data": []map[string]interface{}{},
	}

	// Process each lease
	for i, lease := range leases {
		safeMetro2Log("trace", "Processing lease for Metro2",
			"leaseIndex", i+1,
			"leaseID", lease.ID,
			"propertyID", lease.PropertyID,
			"startDate", lease.StartDate,
			"endDate", lease.EndDate,
			"rentAmount", lease.RentAmount,
			"tenantCount", len(lease.CurrentTenants))

		// Skip lease with no tenants
		if len(lease.CurrentTenants) == 0 {
			safeMetro2Log("debug", "Skipping lease: no tenants", "leaseID", lease.ID)
			continue
		}

		var property *Property
		if user.Role == RoleAdmin {
			// admin can query all properties - use admin function without access control
			property, err = GetPropertyByIDAdmin(ctx, lease.PropertyID)
		} else {
			property, err = GetProperty(ctx, lease.PropertyID, userID)
		}
		if err != nil {
			safeMetro2Log("debug", "Skipping lease: failed to get property info", "leaseID", lease.ID, "error", err)
			continue
		}

		safeMetro2Log("trace", "Property info for Metro2",
			"leaseID", lease.ID,
			"street", property.Address.Street,
			"unit", property.Address.Unit,
			"city", property.Address.City,
			"province", property.Address.Prov,
			"zipCode", property.Address.ZipCode)

		payments, _, err := GetPaymentsUpToDate(ctx, lease.ID, userID, endOfMonth)
		if err != nil {
			safeMetro2Log("debug", "Skipping lease: failed to get payment info", "leaseID", lease.ID, "error", err)
			continue
		}

		safeMetro2Log("trace", "Payment records for Metro2", "leaseID", lease.ID, "paymentCount", len(payments))

		// Collect tenant names for logging
		tenantNames := make([]string, 0, len(lease.CurrentTenants))
		for _, tenant := range lease.CurrentTenants {
			tenantNames = append(tenantNames, fmt.Sprintf("%s %s", tenant.FirstName, tenant.LastName))
		}

		// Create lease process info for logging
		leaseInfo := LeaseProcessInfo{
			LeaseID:         lease.ID,
			PropertyName:    property.Name,
			PropertyAddress: fmt.Sprintf("%s %s, %s, %s %s", property.Address.Street, property.Address.Unit, property.Address.City, property.Address.Prov, property.Address.ZipCode),
			TenantCount:     len(lease.CurrentTenants),
			TenantNames:     tenantNames,
			PaymentCount:    len(payments),
			RentAmount:      lease.RentAmount,
			CurrentBalance:  calculateCurrentBalance(lease, payments, endOfMonth),
			AccountStatus:   calculateAccountStatue(lease, payments, endOfMonth),
			StartDate:       lease.StartDate,
			EndDate:         lease.EndDate,
		}

		// Add to generation log
		generationLog.ProcessedLeases = append(generationLog.ProcessedLeases, leaseInfo)
		generationLog.TotalTenants += len(lease.CurrentTenants)
		generationLog.TotalPayments += len(payments)

		primaryTenant := lease.CurrentTenants[0]
		safeMetro2Log("trace", "Primary tenant info for Metro2",
			"leaseID", lease.ID,
			"tenantID", primaryTenant.ID,
			"firstName", primaryTenant.FirstName,
			"lastName", primaryTenant.LastName,
			"sinNumber", primaryTenant.SINNumber,
			"phone", primaryTenant.Phone)

		// Show joint tenants info
		if len(lease.CurrentTenants) > 1 {
			safeMetro2Log("trace", "Joint tenants info for Metro2",
				"leaseID", lease.ID,
				"jointTenantCount", len(lease.CurrentTenants)-1)
		}

		baseData := map[string]interface{}{
			"recordDescriptorWord":          calculateRDW(leases),
			"processingIndicator":           1, // fixed value
			"timeStamp":                     time.Now().Format("2006-01-02T15:04:05Z"),
			"identificationNumber":          "467RE01193", // fixed value
			"consumerAccountNumber":         strings.TrimSpace(fmt.Sprintf("TN%s", primaryTenant.ID)),
			"portfolioType":                 "O",  // fixed value, Open Account (rent)
			"accountType":                   "29", // fixed value, rental account
			"dateOpened":                    parseDate(lease.StartDate, endOfMonth),
			"highestCredit":                 int(lease.RentAmount),
			"termsDuration":                 "001", // fixed value, 001
			"scheduledMonthlyPaymentAmount": int(lease.RentAmount),
			"actualPaymentAmount":           getActualPaymentAmount(payments, endOfMonth),
			"accountStatus":                 calculateAccountStatue(lease, payments, endOfMonth),
			"paymentHistoryProfile":         calculatePaymentHistory(payments, lease, endOfMonth),
			"specialComment":                "",
			"currentBalance":                calculateCurrentBalance(lease, payments, endOfMonth),
			"dateAccountInformation":        endOfMonth.Format("2006-01-02T00:00:00Z"),
			"surname":                       strings.TrimSpace(primaryTenant.LastName),
			"firstName":                     strings.TrimSpace(primaryTenant.FirstName),
			"middleName":                    strings.TrimSpace(primaryTenant.MiddleName),
			"socialSecurityNumber":          parseSIN(primaryTenant.SINNumber),
			"dateBirth":                     formatDateOfBirth(primaryTenant.DateOfBirth),
			"telephoneNumber":               parsePhoneNumber(primaryTenant.Phone),
			"ecoaCode":                      getEcoaCode(len(lease.CurrentTenants)),
			"countryCode":                   "CA", // fixed value
			"firstLineAddress":              strings.TrimSpace(property.Address.Street),
			"secondLineAddress":             strings.TrimSpace(property.Address.Unit),
			"city":                          strings.TrimSpace(property.Address.City),
			"state":                         strings.TrimSpace(property.Address.Prov),
			"zipCode":                       strings.TrimSpace(property.Address.ZipCode),
			"addressIndicator":              "Y", // fixed value
		}

		// Only add amountPastDue if account status indicates delinquency
		accountStatus := calculateAccountStatue(lease, payments, endOfMonth)
		if accountStatus != "11" && accountStatus != "13" {
			amountPastDue := calculateAmountPastDue(lease, payments, endOfMonth)
			if amountPastDue > 0 {
				baseData["amountPastDue"] = amountPastDue
			}
		}
		// Fill in dateFirstDelinquency only if account status is not "11" (current)
		// Account status "11" means current/paid as agreed, so no delinquency date needed
		if accountStatus != "11" {
			firstDelinquencyDate := getDateFirstDelinquency(lease, payments, endOfMonth)
			if firstDelinquencyDate != "" {
				baseData["dateFirstDelinquency"] = firstDelinquencyDate
			}
		}
		// Only fill in if there are payments
		if getLastPaymentDate(payments, endOfMonth) != "" {
			baseData["dateLastPayment"] = getLastPaymentDate(payments, endOfMonth)
		}
		// Add dateClosed if lease has ended
		if lease.EndDate != "" {
			if leaseEndDate, err := time.Parse("2006-01-02", lease.EndDate); err == nil {
				if endOfMonth.After(leaseEndDate) || endOfMonth.Equal(leaseEndDate) {
					baseData["dateClosed"] = leaseEndDate.Format("2006-01-02T00:00:00Z")
				}
			}
		}
		// J1 segment processing
		var baseSegment map[string]interface{}
		if len(lease.CurrentTenants) > 1 {
			j1Segments := make([]map[string]interface{}, 0)
			for _, tenant := range lease.CurrentTenants[1:] {
				j1Segment := map[string]interface{}{
					"segmentIdentifier":    "J1", // 固定值
					"surname":              strings.TrimSpace(tenant.LastName),
					"firstName":            strings.TrimSpace(tenant.FirstName),
					"generationCode":       "", // 固定为空
					"socialSecurityNumber": parseSIN(tenant.SINNumber),
					"dateBirth":            formatDateOfBirth(tenant.DateOfBirth),
					"telephoneNumber":      parsePhoneNumber(tenant.Phone),
					"ecoaCode":             "2", // 固定值
				}
				j1Segments = append(j1Segments, j1Segment)
			}
			baseSegment = map[string]interface{}{
				"base": baseData,
				"j1":   j1Segments,
			}
		} else {
			baseSegment = map[string]interface{}{
				"base": baseData,
			}
		}
		metro2Data["data"] = append(metro2Data["data"].([]map[string]interface{}), baseSegment)
	}

	safeMetro2Log("info", "Metro2 data generation completed", "finalRecordCount", len(metro2Data["data"].([]map[string]interface{})))

	// Generate JSON data
	jsonData, err := json.Marshal(metro2Data)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to marshal metro2 data: %v", err)
	}

	// Update generation log with final statistics
	generationLog.JSONDataSize = int64(len(jsonData))

	safeMetro2Log("info", "Metro2 generation log summary",
		"totalLeases", generationLog.TotalLeases,
		"processedLeases", len(generationLog.ProcessedLeases),
		"totalTenants", generationLog.TotalTenants,
		"totalPayments", generationLog.TotalPayments,
		"jsonDataSize", generationLog.JSONDataSize)

	return jsonData, generationLog, nil
}

// GetLeasesForMonth returns all leases that were active during the given month
func GetLeasesForMonth(ctx context.Context, reportDate time.Time, userID string) ([]*Lease, error) {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	// 获取用户信息，判断是否admin
	user, err := GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}

	// Calculate start and end of the month
	startOfMonth := time.Date(reportDate.Year(), reportDate.Month(), 1, 0, 0, 0, 0, reportDate.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)

	// Format dates in YYYY-MM-DD format for MongoDB
	startDateStr := startOfMonth.Format("2006-01-02")
	endDateStr := endOfMonth.Format("2006-01-02")

	// Find leases that were active during the month
	filter := bson.M{
		"status":  "active",
		"rentRep": true,
		"$or": []bson.M{
			{
				"startDt": bson.M{"$lte": endDateStr},
				"endDt":   bson.M{"$gte": startDateStr},
			},
			{
				"startDt": bson.M{"$lte": endDateStr},
				"endDt":   "",
			},
		},
	}

	// 不是admin只能查自己的lease
	if user.Role != RoleAdmin {
		filter["usrId"] = userID
	}

	cursor, err := leaseColl.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find leases: %v", err)
	}
	defer cursor.Close(ctx)

	leases := make([]*Lease, 0)
	if err = cursor.All(ctx, &leases); err != nil {
		return nil, fmt.Errorf("failed to decode leases: %v", err)
	}

	return leases, nil
}

// Helper functions
func calculateRDW(leases []*Lease) int {
	// TODO: calculate RDW based on lease
	// 426 + 100 * maximum_tenant_number_of_all_reporting_leases
	// return DefaultMetro2Constants.RecordDescriptorWord
	maxTenants := 1
	for _, lease := range leases {
		if len(lease.CurrentTenants) > maxTenants {
			maxTenants = len(lease.CurrentTenants)
		}
	}
	return 426 + 100*(maxTenants-1)
}

func calculatePaymentHistory(payments []*TenantPayment, lease *Lease, reportMonth time.Time) string {
	// Payment History Codes:
	//0 = Current (paid as agreed)
	//1 = 30-59 days past due
	//2 = 60-89 days past due
	//3 = 90-119 days past due
	//4 = 120-149 days past due
	//5 = 150-179 days past due
	//6 = 180 or more days past due date
	//B = No payment history available prior to this time – either because the account was not open or because the payment history cannot be furnished. A "B" may not be embedded within other values.

	// 解析租约开始和结束日期
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return "BBBBBBBBBBBBBBBBBBBBBBBB"
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			return "BBBBBBBBBBBBBBBBBBBBBBBB"
		}
	}

	// 初始化24个月的历史记录，全部设为B
	history := make([]string, 24)
	for i := 0; i < 24; i++ {
		history[i] = "B"
	}

	// 如果报告月份在租约开始之前，返回全B
	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	if reportMonthStart.Before(leaseStartMonth) {
		return strings.Join(history, "")
	}

	// 如果租约已结束且报告月份在结束日期之后，返回全B
	if !leaseEnd.IsZero() {
		leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
		if reportMonthStart.After(leaseEndMonth) {
			return strings.Join(history, "")
		}
	}

	// 计算从租约开始月份到报告月份前一个月的月数
	// 租约开始月份也需要付款记录
	// 报告月份不包含在payment history中，因为还没有到期
	firstPaymentMonth := leaseStartMonth
	lastHistoryMonth := reportMonthStart.AddDate(0, -1, 0) // 报告月份的前一个月

	// 如果最后历史月份在第一个付款月之前，返回全B
	if lastHistoryMonth.Before(firstPaymentMonth) {
		return strings.Join(history, "")
	}

	monthsFromFirstPayment := 0
	current := firstPaymentMonth
	for current.Before(lastHistoryMonth) || current.Equal(lastHistoryMonth) {
		monthsFromFirstPayment++
		current = current.AddDate(0, 1, 0)
	}

	// 限制月数不超过24
	if monthsFromFirstPayment > 24 {
		monthsFromFirstPayment = 24
	}

	// 首先从最早的月份开始，计算每个月的付款状态
	monthlyStatuses := make([]string, monthsFromFirstPayment)

	// 从最早的付款月份开始，向后检查每个月的付款情况
	for i := monthsFromFirstPayment - 1; i >= 0; i-- {
		// 计算当前位置对应的月份（从报告月份前一个月向前推）
		currentMonth := lastHistoryMonth.AddDate(0, -i, 0)

		// 检查该月份是否在有效付款期间内
		if currentMonth.Before(firstPaymentMonth) {
			monthlyStatuses[i] = "B"
			continue
		}

		if !leaseEnd.IsZero() {
			leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
			if currentMonth.After(leaseEndMonth) {
				monthlyStatuses[i] = "B"
				continue
			}
		}

		// 检查该月份是否有足额付款
		monthlyPayment := 0.0
		for _, payment := range payments {
			paymentMonth := time.Date(payment.Date.Year(), payment.Date.Month(), 1, 0, 0, 0, 0, payment.Date.Location())
			if paymentMonth.Equal(currentMonth) {
				monthlyPayment += payment.Amount
			}
		}

		monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

		if monthlyPayment >= monthlyRent {
			monthlyStatuses[i] = "0" // 足额付款
		} else {
			monthlyStatuses[i] = "unpaid" // 未足额付款，稍后计算逾期程度
		}
	}

	// 现在计算逾期程度：从最早的月份开始累积逾期月数
	cumulativeOverdueMonths := 0
	for i := monthsFromFirstPayment - 1; i >= 0; i-- {
		if monthlyStatuses[i] == "0" {
			// 足额付款，重置累积逾期月数
			cumulativeOverdueMonths = 0
			history[i] = "0"
		} else if monthlyStatuses[i] == "unpaid" {
			// 未足额付款，累积逾期月数增加
			cumulativeOverdueMonths++

			// 根据累积逾期月数确定逾期程度
			switch {
			case cumulativeOverdueMonths == 1:
				history[i] = "1" // 30-59天逾期
			case cumulativeOverdueMonths == 2:
				history[i] = "2" // 60-89天逾期
			case cumulativeOverdueMonths == 3:
				history[i] = "3" // 90-119天逾期
			case cumulativeOverdueMonths == 4:
				history[i] = "4" // 120-149天逾期
			case cumulativeOverdueMonths == 5:
				history[i] = "5" // 150-179天逾期
			default:
				history[i] = "6" // 180+天逾期
			}
		} else {
			// B状态
			history[i] = monthlyStatuses[i]
		}
	}

	return strings.Join(history, "")
}

func calculateAccountStatue(lease *Lease, payments []*TenantPayment, reportMonth time.Time) string {
	// Account Status Codes:
	//  11 = Current account, to be used if the account is not past due and for current accounts
	//  13 = Paid or closed account/zero balance
	//  71 = Account 30-59 days past due
	//  78 = Account 60-89 days past due
	//  80 = Account 90-119 days past due
	//  82 = Account 120-149 days past due
	//  83 = Account 150-179 days past due
	//  84 = Account 180+ days past due

	// 解析租约开始和结束日期
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return "11" // 默认返回当前账户状态
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			return "11"
		}
	}

	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	// 检查租约是否已结束
	if !leaseEnd.IsZero() {
		leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
		if reportMonthStart.After(leaseEndMonth) {
			return "13" // Paid or closed account/zero balance
		}
	}

	// 检查租约是否还未开始
	if reportMonthStart.Before(leaseStartMonth) {
		return "11" // Current account
	}

	// 使用与payment history相同的逻辑
	// 计算从租约开始月份到报告月份前一个月的月数
	firstPaymentMonth := leaseStartMonth
	lastHistoryMonth := reportMonthStart.AddDate(0, -1, 0) // 报告月份的前一个月

	// 如果最后历史月份在第一个付款月之前，返回当前账户状态
	if lastHistoryMonth.Before(firstPaymentMonth) {
		return "11"
	}

	monthsFromFirstPayment := 0
	current := firstPaymentMonth
	for current.Before(lastHistoryMonth) || current.Equal(lastHistoryMonth) {
		monthsFromFirstPayment++
		current = current.AddDate(0, 1, 0)
	}

	// 限制月数不超过24
	if monthsFromFirstPayment > 24 {
		monthsFromFirstPayment = 24
	}

	// 计算累积逾期月数，用于确定账户状态
	cumulativeOverdueMonths := 0

	// 从最早的月份开始，向后检查每个月的付款情况
	for i := monthsFromFirstPayment - 1; i >= 0; i-- {
		// 计算当前位置对应的月份（从报告月份前一个月向前推）
		currentMonth := lastHistoryMonth.AddDate(0, -i, 0)

		// 检查该月份是否在有效付款期间内
		if currentMonth.Before(firstPaymentMonth) {
			continue
		}

		if !leaseEnd.IsZero() {
			leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
			if currentMonth.After(leaseEndMonth) {
				continue
			}
		}

		// 检查该月份是否有足额付款
		monthlyPayment := 0.0
		for _, payment := range payments {
			paymentMonth := time.Date(payment.Date.Year(), payment.Date.Month(), 1, 0, 0, 0, 0, payment.Date.Location())
			if paymentMonth.Equal(currentMonth) {
				monthlyPayment += payment.Amount
			}
		}

		monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

		if monthlyPayment >= monthlyRent {
			// 足额付款，重置累积逾期月数
			cumulativeOverdueMonths = 0
		} else {
			// 未足额付款，累积逾期月数增加
			cumulativeOverdueMonths++
		}
	}

	// 根据累积逾期月数返回账户状态
	switch {
	case cumulativeOverdueMonths == 0:
		return "11" // Current account
	case cumulativeOverdueMonths == 1:
		return "71" // Account 30-59 days past due
	case cumulativeOverdueMonths == 2:
		return "78" // Account 60-89 days past due
	case cumulativeOverdueMonths == 3:
		return "80" // Account 90-119 days past due
	case cumulativeOverdueMonths == 4:
		return "82" // Account 120-149 days past due
	case cumulativeOverdueMonths == 5:
		return "83" // Account 150-179 days past due
	default:
		return "84" // Account 180+ days past due
	}
}

func calculateCurrentBalance(lease *Lease, payments []*TenantPayment, reportMonth time.Time) float64 {
	// Current Balance = Amount Past Due + 当月租金（如果当月没有付款的话）

	// 首先计算Amount Past Due
	amountPastDue := calculateAmountPastDue(lease, payments, reportMonth)

	// 检查报告月份是否有足额付款
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 计算报告月份的付款总额
	currentMonthPayment := 0.0
	for _, payment := range payments {
		paymentMonth := time.Date(payment.Date.Year(), payment.Date.Month(), 1, 0, 0, 0, 0, payment.Date.Location())
		if paymentMonth.Equal(reportMonthStart) {
			currentMonthPayment += payment.Amount
		}
	}

	// 检查租约是否在报告月份有效
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return amountPastDue
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			// 如果结束日期解析失败，忽略结束日期
		}
	}

	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())

	// 如果报告月份在租约开始之前，返回0
	if reportMonthStart.Before(leaseStartMonth) {
		return 0.0
	}

	// 严格按照租约开始日期检查当月租金是否应该包含在余额中
	// 例如：租约15号开始，报告月份是7月，7月的租金在8月15号到期
	// 如果今天是8月14号，还没到8月15号，所以7月的租金不应该包含在Current Balance中
	leaseStartDay := leaseStart.Day()

	// 计算报告月份租金的到期日（下个月的相同日期）
	nextMonth := reportMonthStart.AddDate(0, 1, 0)
	reportMonthRentDueDate := time.Date(nextMonth.Year(), nextMonth.Month(), leaseStartDay, 0, 0, 0, 0, nextMonth.Location())

	// 如果报告月份租金的到期日还没到，不应该包含当月租金
	now := time.Now()
	shouldIncludeCurrentMonthRent := !now.Before(reportMonthRentDueDate)

	// 如果不应该包含当月租金，只返回Amount Past Due
	if !shouldIncludeCurrentMonthRent {
		return amountPastDue
	}

	// 如果租约已结束且报告月份在结束日期之后，返回Amount Past Due
	if !leaseEnd.IsZero() {
		leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
		if reportMonthStart.After(leaseEndMonth) {
			return amountPastDue
		}
	}

	// 计算Current Balance
	currentBalance := amountPastDue

	// 如果当月付款不足，加上当月欠款
	if currentMonthPayment < monthlyRent {
		currentBalance += (monthlyRent - currentMonthPayment)
	}

	// Metro2 规则：负数余额显示为 0（表示多付款）
	if currentBalance < 0 {
		return 0.0
	}

	return currentBalance
}

func calculateAmountPastDue(lease *Lease, payments []*TenantPayment, reportMonth time.Time) float64 {
	// 检查账户状态，如果当前账户则无逾期
	accountStatus := calculateAccountStatue(lease, payments, reportMonth)
	if accountStatus == "11" {
		return 0 // 当前账户，无逾期
	}

	// 检查租约状态
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return 0
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			// 如果结束日期解析失败，忽略结束日期
		}
	}

	// 如果租约已结束或未开始，无逾期
	if (!leaseEnd.IsZero() && reportMonth.After(leaseEnd)) || reportMonth.Before(leaseStart) {
		return 0
	}

	// 计算截止到报告月份前一个月的逾期金额
	// 不包含报告月份及之后的租金，因为还没有到期
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	// 使用与payment history相同的逻辑计算逾期金额
	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())
	firstPaymentMonth := leaseStartMonth
	lastHistoryMonth := reportMonthStart.AddDate(0, -1, 0) // 报告月份的前一个月

	// 如果最后历史月份在第一个付款月之前，无逾期
	if lastHistoryMonth.Before(firstPaymentMonth) {
		return 0
	}

	monthsFromFirstPayment := 0
	current := firstPaymentMonth
	for current.Before(lastHistoryMonth) || current.Equal(lastHistoryMonth) {
		monthsFromFirstPayment++
		current = current.AddDate(0, 1, 0)
	}

	// 限制月数不超过24
	if monthsFromFirstPayment > 24 {
		monthsFromFirstPayment = 24
	}

	// 计算累积逾期金额
	cumulativeOverdueAmount := 0.0
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 从最早的月份开始，向后检查每个月的付款情况
	for i := monthsFromFirstPayment - 1; i >= 0; i-- {
		// 计算当前位置对应的月份（从报告月份前一个月向前推）
		currentMonth := lastHistoryMonth.AddDate(0, -i, 0)

		// 检查该月份是否在有效付款期间内
		if currentMonth.Before(firstPaymentMonth) {
			continue
		}

		if !leaseEnd.IsZero() {
			leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
			if currentMonth.After(leaseEndMonth) {
				continue
			}
		}

		// 检查该月份是否有足额付款
		monthlyPayment := 0.0
		for _, payment := range payments {
			paymentMonth := time.Date(payment.Date.Year(), payment.Date.Month(), 1, 0, 0, 0, 0, payment.Date.Location())
			if paymentMonth.Equal(currentMonth) {
				monthlyPayment += payment.Amount
			}
		}

		if monthlyPayment < monthlyRent {
			// 未足额付款，累积逾期金额
			cumulativeOverdueAmount += (monthlyRent - monthlyPayment)
		} else {
			// 足额付款，重置累积逾期金额
			cumulativeOverdueAmount = 0
		}
	}

	return cumulativeOverdueAmount
}

// parseDate converts a date string to the format expected by Metro2
// maxDate参数确保返回的日期不超过指定的最大日期
func parseDate(dateStr string, maxDate time.Time) string {
	if dateStr == "" {
		// 对于必填日期字段，返回报告月份的最后一天；对于可选字段，调用方应该处理
		return maxDate.Format("2006-01-02T00:00:00Z")
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		// If parsing fails, return report month end date for required fields
		return maxDate.Format("2006-01-02T00:00:00Z")
	}

	// 如果解析的日期超过了最大日期，返回最大日期
	if date.After(maxDate) {
		return maxDate.Format("2006-01-02T00:00:00Z")
	}

	return date.Format("2006-01-02T00:00:00Z")
}

// getLastPaymentDate returns the date of the most recent payment in Metro2 format
// maxDate参数确保返回的日期不超过指定的最大日期
func getLastPaymentDate(payments []*TenantPayment, maxDate time.Time) string {
	if len(payments) == 0 {
		// 如果没有付款记录，返回空字符串，但在使用时需要检查
		return ""
	}

	// Find the most recent payment date that doesn't exceed maxDate
	var latestDate time.Time
	for _, payment := range payments {
		// 只考虑不超过maxDate的payment
		if payment.Date.After(latestDate) && !payment.Date.After(maxDate) {
			latestDate = payment.Date
		}
	}

	// If no valid date found, return empty string
	if latestDate.IsZero() {
		return ""
	}

	return latestDate.Format("2006-01-02T00:00:00Z")
}

func getDateFirstDelinquency(lease *Lease, payments []*TenantPayment, reportMonth time.Time) string {
	// 获取payment history来确定首次逾期日期
	// 注意：即使当前账户状态正常，如果历史上有过逾期，仍需要记录首次逾期日期
	paymentHistory := calculatePaymentHistory(payments, lease, reportMonth)

	// 解析租约开始日期
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return ""
	}

	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	// 从payment history中找到最右边（最早）的逾期记录
	// payment history从左到右表示最新→最旧
	for i := len(paymentHistory) - 1; i >= 0; i-- {
		char := string(paymentHistory[i])

		// 如果是逾期字符（1-6），计算对应的月份
		if char >= "1" && char <= "6" {
			// 计算该位置对应的月份
			// i=0是最新月份（报告月份前一个月），i越大月份越早
			monthsBack := i
			unpaidMonth := reportMonthStart.AddDate(0, -1-monthsBack, 0)

			// 违约日期应该是未付款月份的下一个月
			// 但日期部分应该与Date Opened（租约开始日期）保持一致
			delinquencyMonth := unpaidMonth.AddDate(0, 1, 0)

			// 获取租约开始日期的日期部分
			leaseStartDay := leaseStart.Day()

			// 构造违约日期：使用违约月份，但日期部分与租约开始日期一致
			finalDelinquencyDate := time.Date(
				delinquencyMonth.Year(),
				delinquencyMonth.Month(),
				leaseStartDay,
				0, 0, 0, 0,
				delinquencyMonth.Location(),
			)

			// 确保未付款月份不早于租约开始月份
			if !unpaidMonth.Before(leaseStartMonth) {
				return finalDelinquencyDate.Format("2006-01-02T00:00:00Z")
			}
		}
	}

	// 如果payment history中没有找到逾期记录，返回空字符串
	return ""
}

// formatDateOfBirth formats the date of birth in Metro2 format
func formatDateOfBirth(dob *time.Time) string {
	if dob == nil || dob.IsZero() {
		// DateBirth 是 nullable 字段，但 Metro2 库期望有效的日期格式
		// 使用一个默认的生日日期，比如 1900-01-01
		defaultDate := time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC)
		return defaultDate.Format("2006-01-02T00:00:00Z")
	}

	return dob.Format("2006-01-02T00:00:00Z")
}

// parsePhoneNumber converts a phone string to an integer by removing non-numeric characters
func parsePhoneNumber(phone string) int64 {
	// Remove all non-numeric characters and trim spaces
	phone = strings.TrimSpace(phone)
	var numericOnly string
	for _, char := range phone {
		if char >= '0' && char <= '9' {
			numericOnly += string(char)
		}
	}

	// Convert to int64, ignoring any error
	result, _ := strconv.ParseInt(numericOnly, 10, 64)
	return result
}

// parseSIN converts a SIN string to an integer by removing non-numeric characters
func parseSIN(sin string) int64 {
	// Remove all non-numeric characters and trim spaces
	sin = strings.TrimSpace(sin)
	var numericOnly string
	for _, char := range sin {
		if char >= '0' && char <= '9' {
			numericOnly += string(char)
		}
	}

	// Convert to int64, ignoring any error
	result, _ := strconv.ParseInt(numericOnly, 10, 64)
	return result
}

// getEcoaCode returns the appropriate ECOA code based on number of tenants
func getEcoaCode(numTenants int) string {
	if numTenants > 1 {
		return "2" // Joint account
	}
	return "1" // Individual account
}

// getActualPaymentAmount returns the total payment amount for the report month
func getActualPaymentAmount(payments []*TenantPayment, reportMonth time.Time) int {
	if len(payments) == 0 {
		return 0
	}

	// Calculate total payments for the report month
	totalAmount := 0.0
	for _, payment := range payments {
		if payment.Date.Year() == reportMonth.Year() && payment.Date.Month() == reportMonth.Month() {
			totalAmount += payment.Amount
		}
	}

	return int(totalAmount)
}
