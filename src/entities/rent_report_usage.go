package entities

import (
	"context"
	"fmt"
	"rent_report/models"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// getBaseQuota 从配置文件获取基础配额
func getBaseQuota() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 20 // 默认值
}

// getOverageRate 从配置文件获取超量费率
func getOverageRate() int {
	if rate := goconfig.Config("rentReportUsage.overageRateCAD"); rate != nil {
		if rateInt, ok := rate.(int); ok {
			return rateInt
		}
		if rateInt64, ok := rate.(int64); ok {
			return int(rateInt64)
		}
	}
	return 100 // 默认值：$1.00 CAD = 100分
}

// GetOrCreateUsageRecord 获取或创建当月使用量记录
func GetOrCreateUsageRecord(ctx context.Context, userID string) (*models.RentReportUsage, error) {
	if userID == "" {
		return nil, fmt.Errorf("userID cannot be empty")
	}

	now := time.Now()
	year := now.Year()
	month := int(now.Month())

	coll := gomongo.Coll("rr", "rent_report_usage")
	if coll == nil {
		return nil, fmt.Errorf("rent_report_usage collection not initialized")
	}

	// 尝试查找现有记录（按用户ID、年、月查找）
	var usage models.RentReportUsage
	err := coll.FindOne(ctx, bson.M{
		"uid":   userID,
		"year":  year,
		"month": month,
	}).Decode(&usage)
	if err == nil {
		return &usage, nil
	}

	if err != mongo.ErrNoDocuments {
		return nil, fmt.Errorf("failed to query usage record: %v", err)
	}

	// 创建新记录，使用nanoId作为ID
	recordID := utils.GenerateNanoID()
	usage = models.RentReportUsage{
		ID:           recordID,
		UID:          userID,
		Year:         year,
		Month:        month,
		BaseQuota:    getBaseQuota(),
		UsedCount:    0,
		OverageCount: 0,
		LeaseReports: []models.LeaseReportRecord{},
		Ts:           now,
		Mt:           now,
	}

	_, err = coll.InsertOne(ctx, usage)
	if err != nil {
		return nil, fmt.Errorf("failed to create usage record: %v", err)
	}

	golog.Info("Created new usage record", "userID", userID, "recordID", recordID)
	return &usage, nil
}

// GetUsageRecord 获取指定月份的使用量记录
func GetUsageRecord(ctx context.Context, userID string, year, month int) (*models.RentReportUsage, error) {
	if userID == "" {
		return nil, fmt.Errorf("userID cannot be empty")
	}

	coll := gomongo.Coll("rr", "rent_report_usage")
	if coll == nil {
		return nil, fmt.Errorf("rent_report_usage collection not initialized")
	}

	var usage models.RentReportUsage
	err := coll.FindOne(ctx, bson.M{
		"uid":   userID,
		"year":  year,
		"month": month,
	}).Decode(&usage)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 记录不存在
		}
		return nil, fmt.Errorf("failed to get usage record: %v", err)
	}

	return &usage, nil
}

// UpdateUsageCount 更新使用量计数（手动测试用）
func UpdateUsageCount(ctx context.Context, userID string, usedCount int) error {
	if userID == "" {
		return fmt.Errorf("userID cannot be empty")
	}

	// 获取或创建当月记录
	usage, err := GetOrCreateUsageRecord(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get usage record: %v", err)
	}

	// 计算超量数量
	overageCount := 0
	if usedCount > usage.BaseQuota {
		overageCount = usedCount - usage.BaseQuota
	}

	// 更新记录
	coll := gomongo.Coll("rr", "rent_report_usage")
	if coll == nil {
		return fmt.Errorf("rent_report_usage collection not initialized")
	}

	update := bson.M{
		"$set": bson.M{
			"usedCount":    usedCount,
			"overageCount": overageCount,
			"mt":           time.Now(),
		},
	}

	_, err = coll.UpdateOne(ctx, bson.M{"_id": usage.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to update usage count: %v", err)
	}

	golog.Info("Updated usage count",
		"userID", userID,
		"usedCount", usedCount,
		"overageCount", overageCount)

	return nil
}

// GetUsageStatistics 获取使用量统计信息
func GetUsageStatistics(ctx context.Context, userID string) (*models.UsageStatistics, error) {
	usage, err := GetOrCreateUsageRecord(ctx, userID)
	if err != nil {
		return nil, err
	}

	remainingQuota := usage.BaseQuota - usage.UsedCount
	if remainingQuota < 0 {
		remainingQuota = 0
	}

	stats := &models.UsageStatistics{
		Year:           usage.Year,
		Month:          usage.Month,
		BaseQuota:      usage.BaseQuota,
		UsedCount:      usage.UsedCount,
		OverageCount:   usage.OverageCount,
		RemainingQuota: remainingQuota,
		OverageRate:    float64(getOverageRate()) / 100, // 转换为元
		Currency:       "CAD",
	}

	return stats, nil
}

// GetUsageHistory 获取使用量历史记录
func GetUsageHistory(ctx context.Context, userID string, limit int) ([]models.RentReportUsage, error) {
	if userID == "" {
		return nil, fmt.Errorf("userID cannot be empty")
	}

	coll := gomongo.Coll("rr", "rent_report_usage")
	if coll == nil {
		return nil, fmt.Errorf("rent_report_usage collection not initialized")
	}

	// 按年月倒序排列
	opts := options.Find().
		SetSort(bson.D{{Key: "year", Value: -1}, {Key: "month", Value: -1}}).
		SetLimit(int64(limit))

	cursor, err := coll.Find(ctx, bson.M{"uid": userID}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to query usage history: %v", err)
	}
	defer cursor.Close(ctx)

	var history []models.RentReportUsage
	if err = cursor.All(ctx, &history); err != nil {
		return nil, fmt.Errorf("failed to decode usage history: %v", err)
	}

	return history, nil
}

// LogUsageOperation 记录使用量操作日志
func LogUsageOperation(ctx context.Context, userID, leaseID, action string, isOverage bool, chargeAmount int64) error {
	coll := gomongo.Coll("rr", "usage_operation_log")
	if coll == nil {
		return fmt.Errorf("usage_operation_log collection not initialized")
	}

	now := time.Now()
	usageMonth := fmt.Sprintf("%04d-%02d", now.Year(), now.Month())

	log := models.UsageOperationLog{
		ID:           utils.GenerateNanoID(),
		UID:          userID,
		LeaseID:      leaseID,
		Action:       action,
		UsageMonth:   usageMonth,
		IsOverage:    isOverage,
		ChargeAmount: chargeAmount,
		Timestamp:    now,
	}

	_, err := coll.InsertOne(ctx, log)
	if err != nil {
		return fmt.Errorf("failed to log usage operation: %v", err)
	}

	golog.Info("Logged usage operation",
		"userID", userID,
		"leaseID", leaseID,
		"action", action,
		"isOverage", isOverage,
		"chargeAmount", chargeAmount)

	return nil
}

// CalculateUsageFromMetro2Logs 从Metro2生成日志计算用户的实际使用量
func CalculateUsageFromMetro2Logs(ctx context.Context, userID string, year, month int) (int, error) {
	if userID == "" {
		return 0, fmt.Errorf("userID cannot be empty")
	}

	// 查找指定月份的最后一次Metro2生成记录
	reportMonth := fmt.Sprintf("%04d-%02d", year, month)

	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		return 0, fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	// 查找该月份的所有生成记录，按生成时间倒序排列，取最新的一条
	filter := bson.M{"reportMonth": reportMonth}

	// 先查找所有记录，然后手动排序（因为时间戳可能是不同类型）
	cursor, err := coll.Find(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to query metro2 generation logs: %v", err)
	}
	defer cursor.Close(ctx)

	var allLogs []bson.M
	if err = cursor.All(ctx, &allLogs); err != nil {
		return 0, fmt.Errorf("failed to decode metro2 generation logs: %v", err)
	}

	golog.Info("Found Metro2 logs", "count", len(allLogs), "reportMonth", reportMonth)

	golog.Info("Found Metro2 logs", "count", len(allLogs), "reportMonth", reportMonth)

	if len(allLogs) == 0 {
		// 该月份没有生成Metro2报告，使用量为0
		return 0, nil
	}

	// 找到最新的记录（按生成时间）
	var latestLog bson.M
	var latestTimestamp int64 = -1 // 初始化为-1，确保第一个有效记录会被选中

	for _, log := range allLogs {
		var logTimestamp int64
		var validTimestamp bool

		// 处理不同的时间格式
		if genTime, ok := log["generatedAt"].(time.Time); ok {
			logTimestamp = genTime.UnixNano() / int64(time.Millisecond)
			validTimestamp = true
			golog.Info("Found time.Time format", "logID", log["_id"], "timestamp", logTimestamp)
		} else if genDateTime, ok := log["generatedAt"].(primitive.DateTime); ok {
			// MongoDB primitive.DateTime 类型
			logTimestamp = int64(genDateTime)
			validTimestamp = true
			golog.Info("Found primitive.DateTime format", "logID", log["_id"], "timestamp", logTimestamp)
		} else if genTimeInt, ok := log["generatedAt"].(int64); ok {
			logTimestamp = genTimeInt
			validTimestamp = true
			golog.Info("Found int64 format", "logID", log["_id"], "timestamp", logTimestamp)
		} else if genTimeInt32, ok := log["generatedAt"].(int32); ok {
			logTimestamp = int64(genTimeInt32)
			validTimestamp = true
			golog.Info("Found int32 format", "logID", log["_id"], "timestamp", logTimestamp)
		} else {
			golog.Warn("Invalid generatedAt format", "logID", log["_id"], "generatedAt", log["generatedAt"], "type", fmt.Sprintf("%T", log["generatedAt"]))
			continue // 跳过无效的时间格式
		}

		if validTimestamp && logTimestamp > latestTimestamp {
			latestTimestamp = logTimestamp
			latestLog = log
		}
	}

	if latestLog == nil {
		return 0, fmt.Errorf("no valid metro2 generation log found")
	}

	latestTime := time.Unix(0, latestTimestamp*int64(time.Millisecond))

	golog.Info("Using latest Metro2 generation log",
		"logID", latestLog["_id"],
		"generatedAt", latestTime,
		"totalLeases", latestLog["totalLeases"])

	// 提取ProcessedLeases
	processedLeasesInterface, ok := latestLog["processedLeases"]
	if !ok {
		return 0, nil
	}

	processedLeases, ok := processedLeasesInterface.(bson.A)
	if !ok {
		return 0, nil
	}

	// 获取所有lease的详细信息，统计属于该用户的lease数量
	if len(processedLeases) == 0 {
		return 0, nil
	}

	// 提取所有leaseID
	leaseIDs := make([]string, 0, len(processedLeases))
	for _, lease := range processedLeases {
		if leaseMap, ok := lease.(bson.M); ok {
			if leaseID, ok := leaseMap["leaseId"].(string); ok {
				leaseIDs = append(leaseIDs, leaseID)
			}
		}
	}

	// 查询这些lease的所有者
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return 0, fmt.Errorf("leases collection not initialized")
	}

	leaseCursor, err := leaseColl.Find(ctx, bson.M{
		"_id": bson.M{"$in": leaseIDs},
	})
	if err != nil {
		return 0, fmt.Errorf("failed to query leases: %v", err)
	}
	defer leaseCursor.Close(ctx)

	// 统计属于该用户的lease数量
	userLeaseCount := 0
	for leaseCursor.Next(ctx) {
		var lease struct {
			ID     string `bson:"_id"`
			UserID string `bson:"usrId"`
		}
		if err := leaseCursor.Decode(&lease); err != nil {
			continue
		}
		if lease.UserID == userID {
			userLeaseCount++
		}
	}

	golog.Info("Calculated usage from Metro2 logs",
		"userID", userID,
		"reportMonth", reportMonth,
		"totalProcessedLeases", len(processedLeases),
		"userLeaseCount", userLeaseCount,
		"generationLogID", latestLog["_id"])

	return userLeaseCount, nil
}

// GetUsageStatisticsFromMetro2 从Metro2日志获取使用量统计（新方法）
// 注意：Metro2报告只能生成上个月的数据，所以计费时应该基于上个月的记录
func GetUsageStatisticsFromMetro2(ctx context.Context, userID string) (*models.UsageStatistics, error) {
	// 获取上个月的年月（Metro2只能生成上个月的数据）
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)
	year := lastMonth.Year()
	month := int(lastMonth.Month())

	// 从Metro2日志计算实际使用量
	usedCount, err := CalculateUsageFromMetro2Logs(ctx, userID, year, month)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate usage from Metro2 logs: %v", err)
	}

	baseQuota := getBaseQuota()

	// 计算超量数量
	overageCount := 0
	if usedCount > baseQuota {
		overageCount = usedCount - baseQuota
	}

	// 计算剩余配额
	remainingQuota := baseQuota - usedCount
	if remainingQuota < 0 {
		remainingQuota = 0
	}

	stats := &models.UsageStatistics{
		Year:           year,
		Month:          month,
		BaseQuota:      baseQuota,
		UsedCount:      usedCount,
		OverageCount:   overageCount,
		RemainingQuota: remainingQuota,
		OverageRate:    float64(getOverageRate()) / 100, // 转换为元
		Currency:       "CAD",
	}

	return stats, nil
}

// CheckUsageLimit 检查用户是否可以启用新的租赁报告
func CheckUsageLimit(ctx context.Context, userID string) (bool, *models.UsageStatistics, error) {
	stats, err := GetUsageStatisticsFromMetro2(ctx, userID)
	if err != nil {
		return false, nil, err
	}

	// 检查是否还有剩余配额
	canEnable := stats.RemainingQuota > 0 || stats.UsedCount < stats.BaseQuota

	return canEnable, stats, nil
}
