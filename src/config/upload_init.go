package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

// UploadTypeConfig 单个上传类型的配置
type UploadTypeConfig struct {
	EntryName   string
	TmpPath     string
	StoragePath string
}

// UploadConfig 上传配置结构
type UploadConfig struct {
	Site              string
	LeaseDocuments    string
	PropertyDocuments string
	ProblemReports    string
	Metro2Reports     string
	InvoicePdfs       string // 新增：Invoice PDF 文件
	// 目录配置
	Types map[string]UploadTypeConfig
}

// LoadUploadConfig 从配置文件加载上传配置
func LoadUploadConfig() *UploadConfig {
	site := "TEST" // 默认值

	if configSite := goconfig.Config("userupload.site"); configSite != nil {
		if siteStr, ok := configSite.(string); ok && siteStr != "" {
			site = siteStr
		}
	}

	// 从配置文件中读取各种文档类型的配置
	types := make(map[string]UploadTypeConfig)
	loadUploadTypes(types)

	config := &UploadConfig{
		Site:              site,
		LeaseDocuments:    getEntryNameFromConfig("lease_documents"),
		PropertyDocuments: getEntryNameFromConfig("property_documents"),
		ProblemReports:    getEntryNameFromConfig("problem_reports"),
		Metro2Reports:     getEntryNameFromConfig("metro2_reports"),
		InvoicePdfs:       getEntryNameFromConfig("invoice_pdfs"),
		Types:             types,
	}

	return config
}

// getBaseUploadPath 从配置文件中获取基础上传路径
func getBaseUploadPath() string {
	// 尝试从第一个配置的存储路径中提取基础路径
	uploadConfig := LoadUploadConfig()
	for _, typeConfig := range uploadConfig.Types {
		if typeConfig.StoragePath != "" {
			// 从路径中提取基础部分，例如从 "../uploads/lease_documents" 提取 "../uploads"
			if strings.Contains(typeConfig.StoragePath, "/") {
				parts := strings.Split(typeConfig.StoragePath, "/")
				if len(parts) >= 2 {
					return strings.Join(parts[:len(parts)-1], "/")
				}
			}
		}
	}
	// 如果无法从配置中获取，返回默认值
	return "../uploads"
}

// getBaseTmpPath 从配置文件中获取基础临时路径
func getBaseTmpPath() string {
	// 尝试从第一个配置的临时路径中提取基础路径
	uploadConfig := LoadUploadConfig()
	for _, typeConfig := range uploadConfig.Types {
		if typeConfig.TmpPath != "" {
			// 从路径中提取基础部分，例如从 "../tmp/goupload/lease_docs" 提取 "../tmp/goupload"
			if strings.Contains(typeConfig.TmpPath, "/") {
				parts := strings.Split(typeConfig.TmpPath, "/")
				if len(parts) >= 2 {
					return strings.Join(parts[:len(parts)-1], "/")
				}
			}
		}
	}
	// 如果无法从配置中获取，返回默认值
	return "../tmp/goupload"
}

// loadUploadTypes 从配置文件中加载上传类型配置
func loadUploadTypes(types map[string]UploadTypeConfig) {
	if typesConfig := goconfig.Config("userupload.types"); typesConfig != nil {
		if typesArray, ok := typesConfig.([]interface{}); ok {
			for _, typeConfig := range typesArray {
				if typeMap, ok := typeConfig.(map[string]interface{}); ok {
					var config UploadTypeConfig

					// 读取entryName
					if entryName, exists := typeMap["entryName"]; exists {
						if entryNameStr, ok := entryName.(string); ok {
							config.EntryName = entryNameStr
						}
					}

					// 读取tmpPath
					if tmpPath, exists := typeMap["tmpPath"]; exists {
						if tmpPathStr, ok := tmpPath.(string); ok {
							config.TmpPath = tmpPathStr
						}
					}

					// 读取storage路径
					if storage, exists := typeMap["storage"]; exists {
						if storageArray, ok := storage.([]interface{}); ok && len(storageArray) > 0 {
							if storageMap, ok := storageArray[0].(map[string]interface{}); ok {
								if path, exists := storageMap["path"]; exists {
									if pathStr, ok := path.(string); ok {
										config.StoragePath = pathStr
									}
								}
							}
						}
					}

					// 如果entryName不为空，添加到types map中
					if config.EntryName != "" {
						types[config.EntryName] = config
					}
				}
			}
		}
	}
}

// getEntryNameFromConfig 从配置文件中获取指定的entryName
func getEntryNameFromConfig(defaultName string) string {
	// 尝试从userupload.types数组中找到对应的entryName
	if typesConfig := goconfig.Config("userupload.types"); typesConfig != nil {
		if typesArray, ok := typesConfig.([]interface{}); ok {
			for _, typeConfig := range typesArray {
				if typeMap, ok := typeConfig.(map[string]interface{}); ok {
					if entryName, exists := typeMap["entryName"]; exists {
						if entryNameStr, ok := entryName.(string); ok && entryNameStr == defaultName {
							return entryNameStr
						}
					}
				}
			}
		}
	}

	// 如果配置中没有找到，返回默认值
	return defaultName
}

// InitializeGoupload 初始化 goupload 配置
func InitializeGoupload() error {
	// 创建必要的存储目录
	if err := createUploadDirectories(); err != nil {
		return fmt.Errorf("failed to create upload directories: %v", err)
	}

	// 验证配置
	if err := ValidateGouploadConfig(); err != nil {
		return fmt.Errorf("goupload configuration validation failed: %v", err)
	}

	golog.Info("Goupload initialized successfully")
	return nil
}

// createUploadDirectories 创建必要的上传目录
func createUploadDirectories() error {
	// 从配置中读取目录路径
	uploadConfig := LoadUploadConfig()

	var directories []string

	// 收集所有需要创建的目录
	for _, typeConfig := range uploadConfig.Types {
		if typeConfig.StoragePath != "" {
			directories = append(directories, typeConfig.StoragePath)
		}
		if typeConfig.TmpPath != "" {
			directories = append(directories, typeConfig.TmpPath)
		}
	}

	// 如果配置中没有目录，使用默认目录作为备选
	if len(directories) == 0 {
		golog.Warn("No upload directories found in config, using defaults")
		baseUploadPath := getBaseUploadPath()
		baseTmpPath := getBaseTmpPath()
		directories = []string{
			baseUploadPath + "/lease_documents",
			baseUploadPath + "/property_documents",
			baseUploadPath + "/problem_reports",
			baseUploadPath + "/metro2_reports",
			baseUploadPath + "/invoice_pdfs",
			baseTmpPath + "/lease_docs",
			baseTmpPath + "/property_docs",
			baseTmpPath + "/problem_files",
			baseTmpPath + "/metro2_files",
			baseTmpPath + "/invoice_pdfs",
		}
	}

	for _, dir := range directories {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dir, err)
		}
		golog.Info("Created upload directory", "path", dir)
	}

	return nil
}

// ValidateGouploadConfig 验证 goupload 配置
func ValidateGouploadConfig() error {
	// 从配置中读取目录路径
	uploadConfig := LoadUploadConfig()

	var directories []string

	// 收集所有存储目录进行验证
	for _, typeConfig := range uploadConfig.Types {
		if typeConfig.StoragePath != "" {
			directories = append(directories, typeConfig.StoragePath)
		}
	}

	// 如果配置中没有目录，使用默认目录作为备选
	if len(directories) == 0 {
		golog.Warn("No upload directories found in config for validation, using defaults")
		baseUploadPath := getBaseUploadPath()
		directories = []string{
			baseUploadPath + "/lease_documents",
			baseUploadPath + "/property_documents",
			baseUploadPath + "/problem_reports",
			baseUploadPath + "/metro2_reports",
			baseUploadPath + "/invoice_pdfs",
		}
	}

	for _, dir := range directories {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			return fmt.Errorf("upload directory does not exist: %s", dir)
		}

		// 检查目录是否可写
		testFile := filepath.Join(dir, ".write_test")
		if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
			return fmt.Errorf("directory %s is not writable: %v", dir, err)
		}
		os.Remove(testFile) // 清理测试文件
	}

	golog.Info("Goupload configuration validated successfully")
	return nil
}

// GetStoragePath 获取指定entryName的存储路径
func GetStoragePath(entryName string) string {
	uploadConfig := LoadUploadConfig()
	if typeConfig, exists := uploadConfig.Types[entryName]; exists {
		return typeConfig.StoragePath
	}

	// 返回默认路径
	baseUploadPath := getBaseUploadPath()
	defaultPaths := map[string]string{
		"lease_documents":    baseUploadPath + "/lease_documents",
		"property_documents": baseUploadPath + "/property_documents",
		"problem_reports":    baseUploadPath + "/problem_reports",
		"metro2_reports":     baseUploadPath + "/metro2_reports",
		"invoice_pdfs":       baseUploadPath + "/invoice_pdfs",
	}

	if defaultPath, exists := defaultPaths[entryName]; exists {
		return defaultPath
	}

	return baseUploadPath + "/" + entryName
}

// GetTmpPath 获取指定entryName的临时路径
func GetTmpPath(entryName string) string {
	uploadConfig := LoadUploadConfig()
	if typeConfig, exists := uploadConfig.Types[entryName]; exists {
		return typeConfig.TmpPath
	}

	// 返回默认路径
	baseTmpPath := getBaseTmpPath()
	defaultPaths := map[string]string{
		"lease_documents":    baseTmpPath + "/lease_docs",
		"property_documents": baseTmpPath + "/property_docs",
		"problem_reports":    baseTmpPath + "/problem_files",
		"metro2_reports":     baseTmpPath + "/metro2_files",
		"invoice_pdfs":       baseTmpPath + "/invoice_pdfs",
	}

	if defaultPath, exists := defaultPaths[entryName]; exists {
		return defaultPath
	}

	return baseTmpPath + "/" + entryName
}
