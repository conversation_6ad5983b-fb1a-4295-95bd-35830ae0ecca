package models

import "time"

// BillHst 表示账单历史
type BillHst struct {
	Bid            string    `json:"bid" bson:"_id"` // 账单ID Billing ID
	Uid            string    `json:"uid" bson:"uid"` // 用户ID User ID
	CusId          string    `json:"cusId" bson:"cusId"`
	Amt            int64     `json:"amt" bson:"amt"`                                           // 金额（分） Amount in cents
	Cur            string    `json:"cur" bson:"cur"`                                           // 币种 Currency (如 USD)
	Sts            string    `json:"sts" bson:"sts"`                                           // 状态 Payment Status (succeeded 等)
	Pid            string    `json:"pid" bson:"pid"`                                           // 关联支付ID Linked Payment ID
	PmId           string    `json:"pmId" bson:"pmId"`                                         // 支付方式ID Payment Method ID
	CrdL4          string    `json:"crdL4" bson:"crdL4"`                                       // 卡号后四位 Last 4 digits of card
	InvId          string    `json:"invId,omitempty" bson:"invId,omitempty"`                   // 发票ID Invoice ID (可选)
	InvoicePdfPath string    `json:"invoicePdfPath,omitempty" bson:"invoicePdfPath,omitempty"` // Invoice PDF文件路径 Invoice PDF File Path (可选)
	Dscp           string    `json:"dscp,omitempty" bson:"dscp,omitempty"`                     // 描述 Description (可选)
	Ts             time.Time `json:"ts" bson:"ts"`                                             // 创建时间 Created Timestamp
	Mt             time.Time `json:"mt" bson:"mt"`                                             // 更新时间 Modified Timestamp
}
