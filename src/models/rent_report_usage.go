package models

import "time"

// RentReportUsage 表示用户租赁报告使用量记录
type RentReportUsage struct {
	ID           string                `json:"id" bson:"_id"`           // 使用记录ID，格式：usage_YYYYMM_userId
	UID          string                `json:"uid" bson:"uid"`          // 用户ID
	Year         int                   `json:"year" bson:"year"`        // 年份
	Month        int                   `json:"month" bson:"month"`      // 月份
	BaseQuota    int                   `json:"baseQuota" bson:"baseQuota"`    // 基础配额（通常为20）
	UsedCount    int                   `json:"usedCount" bson:"usedCount"`    // 已使用数量
	OverageCount int                   `json:"overageCount" bson:"overageCount"` // 超量数量
	LeaseReports []LeaseReportRecord   `json:"leaseReports" bson:"leaseReports"` // 租约报告记录
	Ts           time.Time             `json:"ts" bson:"ts"`            // 创建时间
	Mt           time.Time             `json:"mt" bson:"mt"`            // 更新时间
}

// LeaseReportRecord 表示单个租约的报告记录
type LeaseReportRecord struct {
	LeaseID    string     `json:"leaseId" bson:"leaseId"`       // 租约ID
	EnabledAt  time.Time  `json:"enabledAt" bson:"enabledAt"`   // 启用时间
	DisabledAt *time.Time `json:"disabledAt,omitempty" bson:"disabledAt,omitempty"` // 禁用时间，null表示仍然活跃
}

// UsageOperationLog 表示使用量操作日志
type UsageOperationLog struct {
	ID           string    `json:"id" bson:"_id"`                 // 日志ID
	UID          string    `json:"uid" bson:"uid"`                // 用户ID
	LeaseID      string    `json:"leaseId" bson:"leaseId"`        // 租约ID
	Action       string    `json:"action" bson:"action"`          // 操作类型：enable_rent_report, disable_rent_report
	UsageMonth   string    `json:"usageMonth" bson:"usageMonth"`  // 使用月份，格式：YYYY-MM
	IsOverage    bool      `json:"isOverage" bson:"isOverage"`    // 是否为超量使用
	ChargeAmount int64     `json:"chargeAmount" bson:"chargeAmount"` // 费用金额（分），0表示在配额内
	Timestamp    time.Time `json:"timestamp" bson:"timestamp"`    // 操作时间
}

// UsageStatistics 表示使用量统计信息（用于API响应）
type UsageStatistics struct {
	Year           int     `json:"year"`
	Month          int     `json:"month"`
	BaseQuota      int     `json:"baseQuota"`
	UsedCount      int     `json:"usedCount"`
	OverageCount   int     `json:"overageCount"`
	RemainingQuota int     `json:"remainingQuota"`
	OverageRate    float64 `json:"overageRate"`    // 超量费率，如 2.50
	Currency       string  `json:"currency"`       // 币种，如 CAD
}
