package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"rent_report/config"
	"rent_report/entities"
	"rent_report/router"
	"rent_report/services"
	"rent_report/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Metro2ReportController struct{}

func init() {
	router.Register(&Metro2ReportController{})
}

func (c *Metro2ReportController) RegisterRoutes(r *gin.Engine) {
	r.POST("/v1/metro2/generate", c.handleGenerateMetro2)
	r.POST("/v1/metro2/generate-json", c.handleGenerateMetro2JSON)
	r.POST("/api/internal/report-usage", c.ReportUsageInternal)
}

func (c *Metro2ReportController) handleGenerateMetro2(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Parse the request body to get the report month
	var requestBody struct {
		Month string `json:"month"` // Expected format: "YYYY-MM"
	}
	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Parse and validate the report month
	reportMonth, err := time.Parse("2006-01", requestBody.Month)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid month format. Expected: YYYY-MM"})
		return
	}

	golog.Info("Metro2 Generation Debug Information", "userID", userID, "reportMonth", reportMonth.Format("2006-01"))

	// Generate Metro2 file data as JSON（正式数据）
	jsonData, generationLog, err := entities.GenerateMetro2FileData(ctx.Request.Context(), reportMonth, userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 调试输出：解析并显示 JSON 数据的详细字段
	c.debugMetro2Data(jsonData)

	// Generate Metro2 file using utils function
	metro2File, err := utils.GenerateMetro2File(string(jsonData))
	if err != nil {
		golog.Error("Failed to generate Metro2 file", "error", err, "userID", userID)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	golog.Info("Metro2 file generated successfully", "length", len(metro2File), "userID", userID)

	// Save JSON backup file using goupload
	jsonBackupPath, err := c.saveJSONBackupFile(ctx, jsonData, reportMonth, userID)
	if err != nil {
		golog.Error("Failed to save JSON backup file", "error", err, "userID", userID)
		// Continue even if JSON backup saving fails - don't block user download
	} else {
		golog.Info("JSON backup file saved successfully", "path", jsonBackupPath, "userID", userID)
		// Update generation log with JSON backup file path
		generationLog.JSONBackupPath = jsonBackupPath
	}

	// Save Metro2 txt file using goupload
	metro2FilePath, err := c.saveMetro2TxtFile(ctx, metro2File, reportMonth, userID)
	if err != nil {
		golog.Error("Failed to save Metro2 txt file", "error", err, "userID", userID)
		// Continue even if Metro2 txt file saving fails - don't block user download
	} else {
		golog.Info("Metro2 txt file saved successfully", "path", metro2FilePath, "userID", userID)
		// Update generation log with Metro2 txt file path
		generationLog.Metro2FilePath = metro2FilePath
	}

	// Log detailed generation statistics
	golog.Info("Metro2 generation statistics",
		"userID", userID,
		"reportMonth", reportMonth.Format("2006-01"),
		"totalLeases", generationLog.TotalLeases,
		"processedLeases", len(generationLog.ProcessedLeases),
		"totalTenants", generationLog.TotalTenants,
		"totalPayments", generationLog.TotalPayments,
		"jsonDataSize", generationLog.JSONDataSize,
		"metro2FileSize", len(metro2File))

	// Log each processed lease for audit trail
	for i, leaseInfo := range generationLog.ProcessedLeases {
		golog.Info("Metro2 processed lease details",
			"userID", userID,
			"reportMonth", reportMonth.Format("2006-01"),
			"leaseIndex", i+1,
			"leaseID", leaseInfo.LeaseID,
			"propertyName", leaseInfo.PropertyName,
			"tenantCount", leaseInfo.TenantCount,
			"paymentCount", leaseInfo.PaymentCount,
			"rentAmount", leaseInfo.RentAmount,
			"currentBalance", leaseInfo.CurrentBalance,
			"accountStatus", leaseInfo.AccountStatus)
	}

	// Generate download filename with timestamp
	downloadFileName := entities.GenerateMetro2FileName()

	// Update generation log with download filename and file size
	generationLog.FileName = downloadFileName
	generationLog.FileSize = int64(len(metro2File))

	// Save generation log to database (without GridFS file info)
	if err := generationLog.Create(ctx.Request.Context()); err != nil {
		golog.Error("Failed to save Metro2 generation log", "error", err, "userID", userID)
		// Continue even if log saving fails
	} else {
		golog.Info("Metro2 generation log saved successfully",
			"userID", userID,
			"logID", generationLog.ID,
			"reportMonth", generationLog.ReportMonth,
			"totalLeases", generationLog.TotalLeases,
			"totalTenants", generationLog.TotalTenants,
			"totalPayments", generationLog.TotalPayments,
			"downloadFileName", downloadFileName)
	}

	// Metro2生成完成后立即上报使用量（手动生成）
	golog.Info("Metro2 manual generation completed, starting usage reporting",
		"reportMonth", reportMonth.Format("2006-01"),
		"generationLogID", generationLog.ID,
		"userID", userID)

	if err := c.ReportUsageAfterMetro2Generation(ctx.Request.Context(), reportMonth); err != nil {
		golog.Error("Failed to report usage after manual Metro2 generation",
			"error", err,
			"reportMonth", reportMonth.Format("2006-01"),
			"userID", userID)
		// 不返回错误，因为Metro2生成已经成功，使用量上报失败不应该影响用户下载
	}

	// Protect Metro2 data from deletion after successful generation
	if err := c.protectMetro2Data(ctx.Request.Context(), generationLog, userID); err != nil {
		golog.Error("Failed to protect Metro2 data", "error", err, "userID", userID)
		// Continue even if protection fails - don't block user download
	} else {
		golog.Info("Metro2 data protection applied successfully", "userID", userID, "reportMonth", generationLog.ReportMonth)
	}

	// Create Metro2 notification task for delayed email sending
	if err := services.CreateMetro2NotificationTask(ctx.Request.Context(), generationLog); err != nil {
		golog.Error("Failed to create Metro2 notification task", "error", err, "userID", userID, "reportMonth", generationLog.ReportMonth)
		// Continue even if task creation fails - don't block user download
	} else {
		golog.Info("Metro2 notification task created successfully", "userID", userID, "reportMonth", generationLog.ReportMonth)
	}

	// Return the generated file
	ctx.Header("Content-Type", "application/x-metro2")
	ctx.Header("Content-Disposition", "attachment; filename="+downloadFileName)
	ctx.Data(http.StatusOK, "application/x-metro2", metro2File)
}

func (c *Metro2ReportController) handleGenerateMetro2JSON(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Parse the request body to get the report month
	var requestBody struct {
		Month string `json:"month"` // Expected format: "YYYY-MM"
	}
	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Parse the month string
	reportMonth, err := time.Parse("2006-01", requestBody.Month)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid month format. Expected YYYY-MM"})
		return
	}

	golog.Info("Metro2 JSON Generation Request", "userID", userID, "reportMonth", reportMonth.Format("2006-01"))

	// Generate Metro2 file data as JSON
	jsonData, _, err := entities.GenerateMetro2FileData(ctx.Request.Context(), reportMonth, userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Generate download filename for JSON
	now := time.Now()
	downloadFileName := fmt.Sprintf("RM-Metro2-Backup-%s-%s.json", reportMonth.Format("2006-01"), now.Format("20060102-150405"))

	golog.Info("Metro2 JSON file generated successfully", "length", len(jsonData), "userID", userID, "fileName", downloadFileName)

	// Return the JSON file
	ctx.Header("Content-Type", "application/json")
	ctx.Header("Content-Disposition", "attachment; filename="+downloadFileName)
	ctx.Data(http.StatusOK, "application/json", jsonData)
}

// debugMetro2Data 解析并输出 Metro2 JSON 数据的详细字段
func (c *Metro2ReportController) debugMetro2Data(jsonData []byte) {
	var data map[string]interface{}
	if err := json.Unmarshal(jsonData, &data); err != nil {
		golog.Error("Failed to parse JSON data", "error", err)
		return
	}

	golog.Debug("JSON data total length", "bytes", len(jsonData))

	// 输出 Header 信息
	if header, ok := data["header"].(map[string]interface{}); ok {
		golog.Debug("Metro2 Header Information",
			"recordDescriptorWord", header["recordDescriptorWord"],
			"recordIdentifier", header["recordIdentifier"],
			"equifaxProgramIdentifier", header["EquifaxProgramIdentifier"],
			"activityDate", header["activityDate"],
			"reporterName", header["reporterName"])
	}

	// 输出 Data 记录信息
	if dataRecords, ok := data["data"].([]interface{}); ok {
		golog.Debug("Metro2 Data Record Information", "totalRecords", len(dataRecords))

		for i, record := range dataRecords {
			if recordMap, ok := record.(map[string]interface{}); ok {
				// 输出 Base 段信息
				if base, ok := recordMap["base"].(map[string]interface{}); ok {
					golog.Debug("Metro2 Base Segment",
						"recordIndex", i+1,
						"consumerAccountNumber", base["consumerAccountNumber"],
						"accountType", base["accountType"],
						"accountStatus", base["accountStatus"],
						"currentBalance", base["currentBalance"],
						"surname", base["surname"],
						"firstName", base["firstName"],
						"socialSecurityNumber", base["socialSecurityNumber"])

					// 修正：J1 段应在与 base 同级
					if j1Segments, ok := recordMap["j1"].([]interface{}); ok && len(j1Segments) > 0 {
						golog.Debug("Metro2 J1 Segment (Joint Tenants)", "recordIndex", i+1, "jointTenantCount", len(j1Segments))
						for j, j1Segment := range j1Segments {
							if j1Map, ok := j1Segment.(map[string]interface{}); ok {
								golog.Debug("Metro2 J1 Segment Details",
									"recordIndex", i+1,
									"jointTenantIndex", j+1,
									"surname", j1Map["surname"],
									"firstName", j1Map["firstName"],
									"socialSecurityNumber", j1Map["socialSecurityNumber"])
							}
						}
					} else {
						golog.Debug("Metro2 J1 Segment", "recordIndex", i+1, "jointTenants", "none")
					}
				}
			}
		}
	}
}

// ReportUsageInternal 内部API：处理使用量上报请求
func (c *Metro2ReportController) ReportUsageInternal(ctx *gin.Context) {
	// 验证内部API调用
	if ctx.GetHeader("X-Internal-API") != "true" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized internal API call"})
		return
	}

	var request struct {
		ReportMonth string `json:"reportMonth" binding:"required"`
		Source      string `json:"source"`
	}

	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 解析报告月份
	reportMonth, err := time.Parse("2006-01", request.ReportMonth)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid reportMonth format, expected YYYY-MM"})
		return
	}

	golog.Info("Internal usage reporting request received",
		"reportMonth", request.ReportMonth,
		"source", request.Source)

	// 调用使用量上报逻辑
	if err := c.ReportUsageAfterMetro2Generation(ctx.Request.Context(), reportMonth); err != nil {
		golog.Error("Internal usage reporting failed",
			"error", err,
			"reportMonth", request.ReportMonth,
			"source", request.Source)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to report usage"})
		return
	}

	golog.Info("Internal usage reporting completed successfully",
		"reportMonth", request.ReportMonth,
		"source", request.Source)

	ctx.JSON(http.StatusOK, gin.H{
		"success":     true,
		"message":     "Usage reporting completed",
		"reportMonth": request.ReportMonth,
		"source":      request.Source,
	})
}

// saveJSONBackupFile saves the JSON backup file using goupload and returns the file path
func (c *Metro2ReportController) saveJSONBackupFile(ctx *gin.Context, jsonData []byte, reportMonth time.Time, userID string) (string, error) {
	golog.Info("Starting JSON backup file save using goupload",
		"userID", userID,
		"reportMonth", reportMonth.Format("2006-01"),
		"jsonSize", len(jsonData))

	// Generate filename for JSON backup with new naming format
	now := time.Now()
	fileName := fmt.Sprintf("RM-Metro2-Backup-%s-%s.json", reportMonth.Format("2006-01"), now.Format("20060102-150405"))

	golog.Info("Generated JSON backup filename", "fileName", fileName, "userID", userID)

	// Create a reader from JSON data
	jsonReader := bytes.NewReader(jsonData)

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.Metro2Reports, statsColl)
	if err != nil {
		golog.Error("Failed to create stats updater for JSON backup", "error", err, "userID", userID)
		return "", fmt.Errorf("failed to create stats updater: %v", err)
	}

	// Use goupload to save the JSON backup file
	result, err := goupload.Upload(
		ctx.Request.Context(),
		statsUpdater,               // statsUpdater
		uploadConfig.Site,          // site (从配置文件读取)
		uploadConfig.Metro2Reports, // entryName (从配置文件读取)
		userID,                     // uid
		jsonReader,                 // reader
		fileName,                   // originalFilename
		int64(len(jsonData)),       // clientDeclaredSize
	)
	if err != nil {
		golog.Error("Failed to upload JSON backup file", "error", err, "userID", userID, "fileName", fileName)
		return "", fmt.Errorf("failed to upload JSON backup file: %v", err)
	}

	golog.Info("JSON backup file successfully saved using goupload",
		"userID", userID,
		"fileName", fileName,
		"path", result.Path,
		"size", result.Size,
		"reportMonth", reportMonth.Format("2006-01"))

	return result.Path, nil
}

// saveMetro2TxtFile saves the Metro2 txt file using goupload and returns the file path
func (c *Metro2ReportController) saveMetro2TxtFile(ctx *gin.Context, metro2File []byte, reportMonth time.Time, userID string) (string, error) {
	golog.Info("Starting Metro2 txt file save using goupload",
		"userID", userID,
		"reportMonth", reportMonth.Format("2006-01"),
		"fileSize", len(metro2File))

	// Generate filename for Metro2 txt file with new naming format
	now := time.Now()
	fileName := fmt.Sprintf("RM-Metro2-Archive-%s-%s.txt", reportMonth.Format("2006-01"), now.Format("20060102-150405"))

	golog.Info("Generated Metro2 txt filename", "fileName", fileName, "userID", userID)

	// Create a reader from Metro2 file data
	metro2Reader := bytes.NewReader(metro2File)

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, "metro2_reports", statsColl)
	if err != nil {
		golog.Error("Failed to create stats updater for Metro2 txt", "error", err, "userID", userID)
		return "", fmt.Errorf("failed to create stats updater: %v", err)
	}

	// Use goupload to save the Metro2 txt file
	result, err := goupload.Upload(
		ctx.Request.Context(),
		statsUpdater,               // statsUpdater
		uploadConfig.Site,          // site (从配置文件读取)
		uploadConfig.Metro2Reports, // entryName (从配置文件读取)
		userID,                     // uid
		metro2Reader,               // reader
		fileName,                   // originalFilename
		int64(len(metro2File)),     // clientDeclaredSize
	)
	if err != nil {
		golog.Error("Failed to upload Metro2 txt file", "error", err, "userID", userID, "fileName", fileName)
		return "", fmt.Errorf("failed to upload Metro2 txt file: %v", err)
	}

	golog.Info("Metro2 txt file successfully saved using goupload",
		"userID", userID,
		"fileName", fileName,
		"path", result.Path,
		"size", result.Size,
		"reportMonth", reportMonth.Format("2006-01"))

	return result.Path, nil
}

// protectMetro2Data protects all data used in Metro2 generation from deletion
func (c *Metro2ReportController) protectMetro2Data(ctx context.Context, generationLog *entities.Metro2GenerationLog, userID string) error {
	golog.Info("Starting Metro2 data protection", "userID", userID, "reportMonth", generationLog.ReportMonth)

	// Collect all lease IDs from processed leases
	leaseIDs := make([]string, 0, len(generationLog.ProcessedLeases))
	for _, leaseInfo := range generationLog.ProcessedLeases {
		leaseIDs = append(leaseIDs, leaseInfo.LeaseID)
	}

	if len(leaseIDs) == 0 {
		golog.Info("No leases to protect", "userID", userID)
		return nil
	}

	// 1. Protect payments used in Metro2
	if err := c.protectPayments(ctx, leaseIDs, userID); err != nil {
		return fmt.Errorf("failed to protect payments: %v", err)
	}

	// 2. Protect tenants used in Metro2
	if err := c.protectTenants(ctx, leaseIDs, userID); err != nil {
		return fmt.Errorf("failed to protect tenants: %v", err)
	}

	// 3. Protect lease documents
	if err := c.protectLeaseDocuments(ctx, leaseIDs, userID); err != nil {
		return fmt.Errorf("failed to protect lease documents: %v", err)
	}

	golog.Info("Metro2 data protection completed successfully",
		"userID", userID,
		"reportMonth", generationLog.ReportMonth,
		"protectedLeases", len(leaseIDs))

	return nil
}

// protectPayments protects all payments for the given leases
func (c *Metro2ReportController) protectPayments(ctx context.Context, leaseIDs []string, userID string) error {
	paymentColl := gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return fmt.Errorf("tenant_payments collection not initialized")
	}

	// Update all payments for these leases to be protected
	filter := bson.M{
		"leaseId": bson.M{"$in": leaseIDs},
	}
	update := bson.M{
		"$set": bson.M{
			"isProtected": true,
		},
	}

	result, err := paymentColl.UpdateMany(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to protect payments: %v", err)
	}

	golog.Info("Protected payments for Metro2",
		"userID", userID,
		"leaseCount", len(leaseIDs),
		"protectedPayments", result.ModifiedCount)

	return nil
}

// protectTenants protects tenants that are in ctnts arrays (both in lease table and tenant table)
func (c *Metro2ReportController) protectTenants(ctx context.Context, leaseIDs []string, userID string) error {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 1. First, collect all tenant IDs that are in ctnts arrays
	var currentTenantIDs []string

	// Get all leases to extract current tenant IDs
	cursor, err := leaseColl.Find(ctx, bson.M{
		"_id": bson.M{"$in": leaseIDs},
	})
	if err != nil {
		return fmt.Errorf("failed to find leases: %v", err)
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var lease struct {
			CurrentTenants []struct {
				ID string `bson:"_id"`
			} `bson:"ctnts"`
		}
		if err := cursor.Decode(&lease); err != nil {
			continue
		}

		for _, tenant := range lease.CurrentTenants {
			currentTenantIDs = append(currentTenantIDs, tenant.ID)
		}
	}

	if len(currentTenantIDs) == 0 {
		golog.Info("No current tenants found to protect", "userID", userID)
		return nil
	}

	// 2. Protect tenants in lease ctnts arrays only (not ptnts)
	leaseFilter := bson.M{
		"_id": bson.M{"$in": leaseIDs},
	}
	leaseUpdate := bson.M{
		"$set": bson.M{
			"ctnts.$[].isProtected": true,
		},
	}

	leaseResult, err := leaseColl.UpdateMany(ctx, leaseFilter, leaseUpdate)
	if err != nil {
		return fmt.Errorf("failed to protect lease current tenants: %v", err)
	}

	// 3. Protect corresponding records in tenant table (only those in ctnts)
	tenantColl := gomongo.Coll("rr", "tenants")
	if tenantColl == nil {
		return fmt.Errorf("tenants collection not initialized")
	}

	tenantFilter := bson.M{
		"_id": bson.M{"$in": currentTenantIDs},
	}
	tenantUpdate := bson.M{
		"$set": bson.M{
			"isProtected": true,
		},
	}

	tenantResult, err := tenantColl.UpdateMany(ctx, tenantFilter, tenantUpdate)
	if err != nil {
		return fmt.Errorf("failed to protect tenant records: %v", err)
	}

	golog.Info("Protected current tenants for Metro2",
		"userID", userID,
		"leaseCount", len(leaseIDs),
		"currentTenantIDs", len(currentTenantIDs),
		"protectedLeases", leaseResult.ModifiedCount,
		"protectedTenantRecords", tenantResult.ModifiedCount)

	return nil
}

// protectLeaseDocuments protects all normal status documents for the given leases
func (c *Metro2ReportController) protectLeaseDocuments(ctx context.Context, leaseIDs []string, userID string) error {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// Update all normal status documents in these leases to be protected
	filter := bson.M{
		"_id":              bson.M{"$in": leaseIDs},
		"documents.status": "normal",
	}
	update := bson.M{
		"$set": bson.M{
			"documents.$[elem].isProtected": true,
		},
	}

	// Array filter to only update normal status documents
	arrayFilters := options.ArrayFilters{
		Filters: []interface{}{
			bson.M{"elem.status": "normal"},
		},
	}

	result, err := leaseColl.UpdateMany(ctx, filter, update, options.Update().SetArrayFilters(arrayFilters))
	if err != nil {
		return fmt.Errorf("failed to protect lease documents: %v", err)
	}

	golog.Info("Protected lease documents for Metro2",
		"userID", userID,
		"leaseCount", len(leaseIDs),
		"protectedLeases", result.ModifiedCount)

	return nil
}

// ReportUsageAfterMetro2Generation Metro2生成完成后立即上报使用量（公共方法，用于测试）
func (c *Metro2ReportController) ReportUsageAfterMetro2Generation(ctx context.Context, reportMonth time.Time) error {
	golog.Info("Starting usage reporting after Metro2 generation (manual)",
		"reportMonth", reportMonth.Format("2006-01"))

	// 创建使用量报告服务
	usageService := services.NewMonthlyUsageReportService()

	// 获取报告月份的年月
	year := reportMonth.Year()
	month := int(reportMonth.Month())

	// 获取所有用户的使用量（基于刚生成的Metro2记录）
	userUsageMap, err := usageService.CalculateAllUserUsageFromMetro2(ctx, year, month)
	if err != nil {
		return fmt.Errorf("failed to calculate user usage from Metro2: %v", err)
	}

	golog.Info("Calculated user usage from Metro2 (manual)",
		"userCount", len(userUsageMap),
		"reportMonth", reportMonth.Format("2006-01"))

	if len(userUsageMap) == 0 {
		golog.Info("No user usage found, skipping usage reporting (manual)")
		return nil
	}

	// 获取基础配额
	baseQuota := usageService.GetBaseQuota()

	successCount := 0
	errorCount := 0

	// 为每个有超量使用的用户报告到Stripe
	for userID, usageCount := range userUsageMap {
		if usageCount > baseQuota {
			overageCount := usageCount - baseQuota

			// 手动生成时不检查防重复，因为用户可能多次生成，且Stripe meter已改为只算最后一次
			golog.Info("Reporting usage to Stripe (manual generation)",
				"userID", userID,
				"totalUsage", usageCount,
				"overageCount", overageCount,
				"reportMonth", reportMonth.Format("2006-01"))

			err := usageService.ReportSingleUserUsage(ctx, userID, overageCount, year, month)
			if err != nil {
				golog.Error("Failed to report usage to Stripe (manual)",
					"error", err,
					"userID", userID,
					"overageCount", overageCount,
					"reportMonth", reportMonth.Format("2006-01"))
				errorCount++
			} else {
				golog.Info("Successfully reported usage to Stripe (manual)",
					"userID", userID,
					"totalUsage", usageCount,
					"overageCount", overageCount,
					"reportMonth", reportMonth.Format("2006-01"))

				// 记录已上报状态（更新为最新的上报记录）
				c.recordUsageReported(ctx, userID, year, month, overageCount, "manual")
				successCount++
			}
		} else {
			golog.Debug("No overage usage for user (manual)",
				"userID", userID,
				"usage", usageCount,
				"baseQuota", baseQuota)
		}
	}

	golog.Info("Usage reporting completed after Metro2 generation (manual)",
		"successCount", successCount,
		"errorCount", errorCount,
		"totalUsers", len(userUsageMap),
		"reportMonth", reportMonth.Format("2006-01"))

	return nil
}

// hasReportedUsageThisMonth 检查是否已经上报过这个月的使用量
func (c *Metro2ReportController) hasReportedUsageThisMonth(ctx context.Context, userID string, year, month int) bool {
	coll := gomongo.Coll("rr", "usage_reports")
	if coll == nil {
		golog.Warn("usage_reports collection not initialized")
		return false
	}

	reportMonth := fmt.Sprintf("%04d-%02d", year, month)

	var result bson.M
	err := coll.FindOne(ctx, bson.M{
		"userID":      userID,
		"reportMonth": reportMonth,
	}).Decode(&result)

	return err == nil // 如果找到记录，说明已经上报过
}

// recordUsageReported 记录已上报的使用量
func (c *Metro2ReportController) recordUsageReported(ctx context.Context, userID string, year, month int, overageCount int, reportType string) {
	coll := gomongo.Coll("rr", "usage_reports")
	if coll == nil {
		golog.Warn("usage_reports collection not initialized")
		return
	}

	reportMonth := fmt.Sprintf("%04d-%02d", year, month)

	record := bson.M{
		"userID":       userID,
		"reportMonth":  reportMonth,
		"overageCount": overageCount,
		"reportedAt":   time.Now(),
		"reportType":   reportType, // "manual", "auto_after_metro2", etc.
	}

	// 使用upsert确保不重复插入
	_, err := coll.ReplaceOne(ctx, bson.M{
		"userID":      userID,
		"reportMonth": reportMonth,
	}, record, &options.ReplaceOptions{
		Upsert: &[]bool{true}[0],
	})

	if err != nil {
		golog.Error("Failed to record usage report",
			"error", err,
			"userID", userID,
			"reportMonth", reportMonth,
			"reportType", reportType)
	} else {
		golog.Debug("Usage report recorded",
			"userID", userID,
			"reportMonth", reportMonth,
			"overageCount", overageCount,
			"reportType", reportType)
	}
}
