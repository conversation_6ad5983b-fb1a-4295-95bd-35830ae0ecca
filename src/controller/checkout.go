package controller

import (
	"encoding/json"
	"net/http"

	//"os"
	"time"

	"rent_report/entities"
	"rent_report/models"
	"rent_report/router"
	"rent_report/utils"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/v75"
	"github.com/stripe/stripe-go/v75/checkout/session"
	"github.com/stripe/stripe-go/v75/price"
	"go.mongodb.org/mongo-driver/bson"
)

type CheckoutController struct{}

func init() {
	router.Register(&CheckoutController{})
}

// getExtraReportPriceId 从配置文件获取extra_report价格ID
func getExtraReportPriceId() string {
	if priceId := goconfig.Config("stripe.extra_report_price_id"); priceId != nil {
		if priceIdStr, ok := priceId.(string); ok && priceIdStr != "" {
			return priceIdStr
		}
	}
	return "price_1RuwwtRdRW2qyPyrLm0nLLG0" // 默认值
}

// getExtraReportProductId 从配置文件获取extra_report产品ID
func getExtraReportProductId() string {
	if productId := goconfig.Config("stripe.extra_report_product_id"); productId != nil {
		if productIdStr, ok := productId.(string); ok && productIdStr != "" {
			return productIdStr
		}
	}
	return "prod_SqeFaz9P1fDdOq" // 默认值
}

func (c *CheckoutController) RegisterRoutes(r *gin.Engine) {
	r.POST("/v1/checkout/session", handleCreateCheckoutSession)
}

// handleCreateCheckoutSession 创建Stripe Checkout Session
func handleCreateCheckoutSession(c *gin.Context) {
	golog.Info("🚀 CHECKOUT SESSION STARTED - NEW VERSION WITH DUAL SUBSCRIPTION SUPPORT")
	//stripe.Key = os.Getenv("STRIPE_SECRET_KEY")

	var req struct {
		PriceId    string `json:"priceId" binding:"required"`
		SuccessUrl string `json:"successUrl" binding:"required"`
		CancelUrl  string `json:"cancelUrl" binding:"required"`
		PrdId      string `json:"prdId" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "wrong parameters"})
		return
	}

	// 从token获取当前用户id
	uid, err := utils.GetUserIDFromToken(c.Request)
	if err != nil || uid == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未登录或token无效"})
		return
	}
	golog.Info("Current user ID", "uid", uid)

	var discountPercent float64
	var usage entities.ReferralCodeUsage
	err = gomongo.Coll("rr", "referral_code_usages").FindOne(c.Request.Context(), bson.M{
		"uid":    uid,
		"status": "active",
		"exp":    bson.M{"$gt": time.Now()},
	}).Decode(&usage)

	if err == nil {
		var code entities.ReferralCode
		err = gomongo.Coll("rr", "referral_codes").FindOne(c.Request.Context(), bson.M{
			"_id":    usage.CodeID,
			"status": "active",
			"exp":    bson.M{"$gt": time.Now()},
		}).Decode(&code)

		if err == nil {
			type Dscp struct {
				DiscountPercent float64 `json:"discount_percent"`
			}
			var d Dscp
			if json.Unmarshal([]byte(code.Dscp), &d) == nil {
				discountPercent = d.DiscountPercent
			}
		}
	}

	finalPriceId := req.PriceId

	// 获取订阅计划信息
	var subPlan models.SubPlan
	err = gomongo.Coll("rr", "subplan").FindOne(c.Request.Context(), bson.M{"prdId": req.PrdId}).Decode(&subPlan)
	if err == nil {
		needCreateNewPrice := false
		originalPrice := float64(subPlan.Prc)
		discountMultiplier := 1.0

		// 如果是年付计划，应用20%年费折扣
		if subPlan.Intrvl == "year" {
			discountMultiplier = 0.8 // 20% off = 0.8倍
			needCreateNewPrice = true
		}

		// 如果有推荐码折扣，再乘以推荐码折扣
		if discountPercent > 0 {
			discountMultiplier = discountMultiplier * (1.0 - discountPercent/100.0)
			needCreateNewPrice = true
		}

		finalPrice := float64(int64(originalPrice*discountMultiplier + 0.5)) // 四舍五入到分

		// 如果需要创建新价格（有年费折扣或推荐码折扣）
		if needCreateNewPrice {
			newPriceInCents := int64(finalPrice)

			priceParams := &stripe.PriceParams{
				Product:    stripe.String(req.PrdId),
				UnitAmount: stripe.Int64(newPriceInCents),
				Currency:   stripe.String(string(stripe.CurrencyCAD)), // Assuming currency is CAD
				Recurring: &stripe.PriceRecurringParams{
					Interval: stripe.String(subPlan.Intrvl),
				},
			}
			newPrice, err := price.New(priceParams)
			if err == nil {
				finalPriceId = newPrice.ID
			}
		}
	}

	// 准备line items：基础订阅
	lineItems := []*stripe.CheckoutSessionLineItemParams{
		{
			Price:    stripe.String(finalPriceId),
			Quantity: stripe.Int64(1),
		},
	}

	// 检查是否为年费订阅 - 通过订阅计划的计费周期判断
	isAnnualBilling := false
	golog.Info("🔍 Checking subscription plan",
		"uid", uid,
		"prdId", req.PrdId,
		"planInterval", subPlan.Intrvl,
		"planName", subPlan.Nm)

	if subPlan.Intrvl == "year" {
		isAnnualBilling = true
		golog.Info("✅ Creating annual subscription checkout", "uid", uid, "prdId", req.PrdId)
	} else {
		golog.Info("📅 Creating monthly subscription checkout", "uid", uid, "prdId", req.PrdId)
	}

	// 只有月费订阅才在checkout时添加extra_report价格
	// 年费订阅由于计费周期不同，会在有超量使用时通过webhook单独处理
	if !isAnnualBilling {
		lineItems = append(lineItems, &stripe.CheckoutSessionLineItemParams{
			// 添加extra_report的metered价格 - metered类型不能设置quantity
			Price: stripe.String(getExtraReportPriceId()), // 从配置文件读取
		})
		golog.Info("Added extra_report price to checkout", "extraReportPriceId", getExtraReportPriceId())
	} else {
		golog.Info("Skipped extra_report price for annual subscription - will be handled separately")
	}

	// 准备metadata
	metadata := map[string]string{
		"uid":   uid,
		"prdId": req.PrdId,
	}

	// 只有月费订阅才添加extraReportProductId到metadata
	// 年费订阅会在webhook中单独处理超量报告
	if !isAnnualBilling {
		metadata["extraReportProductId"] = getExtraReportProductId()
		golog.Info("📝 Added extraReportProductId to metadata", "extraReportProductId", getExtraReportProductId())
	} else {
		// 为年费订阅添加特殊标识，便于webhook处理
		metadata["billingType"] = "annual"
		golog.Info("🏷️ Added annual billingType to metadata", "billingType", "annual")
	}

	golog.Info("📋 Final metadata", "metadata", metadata)

	params := &stripe.CheckoutSessionParams{
		PaymentMethodTypes:       stripe.StringSlice([]string{"card"}),
		Mode:                     stripe.String(string(stripe.CheckoutSessionModeSubscription)),
		BillingAddressCollection: stripe.String("required"),
		LineItems:                lineItems,
		SuccessURL:               stripe.String(req.SuccessUrl),
		CancelURL:                stripe.String(req.CancelUrl),
		Metadata:                 metadata,
	}

	s, err := session.New(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"url": s.URL})
}
