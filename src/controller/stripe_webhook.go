package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
	"time"

	"rent_report/entities"
	"rent_report/models"
	"rent_report/router"
	"rent_report/services"
	"rent_report/utils"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/paymentintent"
	"github.com/stripe/stripe-go/v75"
	"github.com/stripe/stripe-go/v75/webhook"
	"go.mongodb.org/mongo-driver/bson"
)

type StripeWebhookController struct{}

func init() {
	router.Register(&StripeWebhookController{})
}

func (c *StripeWebhookController) RegisterRoutes(r *gin.Engine) {
	r.POST("/v1/stripe/webhook", handleStripeWebhook)
}

func saveStripeEventLog(ctx context.Context, event stripe.Event, raw string, status string, pid string) {
	coll := gomongo.Coll("rr", "stripeeventlog")
	if coll == nil {
		return
	}
	log := models.StripeEventLog{
		Eid: utils.GenerateNanoID(),
		Tp:  string(event.Type),
		Sts: status,
		Raw: raw,
		Pid: pid,
		Ts:  time.Now(),
		Mt:  time.Now(),
	}
	_, _ = coll.InsertOne(ctx, log)
}

// handleStripeWebhook 监听并处理Stripe Webhook事件
func handleStripeWebhook(c *gin.Context) {
	const MaxBodyBytes = int64(65536)
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, MaxBodyBytes)
	payload, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Read error"})
		return
	}

	// Stripe签名校验（必须配置STRIPE_WEBHOOK_SECRET环境变量或配置文件）
	secret := os.Getenv("STRIPE_WEBHOOK_SECRET")
	if secret == "" {
		// 从配置文件读取作为备选
		if configSecret := goconfig.Config("stripe.webhook_secret"); configSecret != nil {
			if secretStr, ok := configSecret.(string); ok && secretStr != "" {
				secret = secretStr
				golog.Debug("Using webhook secret from config file")
			}
		}
	} else {
		golog.Debug("Using webhook secret from environment variable")
	}

	// 强制要求webhook secret，绝不跳过签名验证
	if secret == "" {
		golog.Error("Webhook secret not configured")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Webhook secret not configured"})
		return
	}

	// 验证Stripe签名
	sigHeader := c.GetHeader("Stripe-Signature")
	golog.Debug("Stripe webhook signature verification", "hasSignature", sigHeader != "", "secretLength", len(secret))

	// 使用 ConstructEventWithOptions 忽略API版本不匹配
	event, err := webhook.ConstructEventWithOptions(payload, sigHeader, secret, webhook.ConstructEventOptions{
		IgnoreAPIVersionMismatch: true,
	})
	if err != nil {
		golog.Error("Signature verification failed", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Signature verification failed: " + err.Error()})
		return
	}

	golog.Debug("Webhook signature verification successful")
	// if secret != "" {
	// 	sigHeader := c.GetHeader("Stripe-Signature")
	// 	event, err = webhook.ConstructEvent(payload, sigHeader, secret)
	// 	if err != nil {
	// 		c.JSON(http.StatusBadRequest, gin.H{"error": "Signature verification failed"})
	// 		return
	// 	}
	// } else {
	// 	if err := json.Unmarshal(payload, &event); err != nil {
	// 		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
	// 		return
	// 	}
	// }

	saveStripeEventLog(c.Request.Context(), event, string(payload), "received", "")

	switch event.Type {
	case "checkout.session.completed":
		golog.Info("🎯 WEBHOOK: checkout.session.completed event received")
		var session stripe.CheckoutSession
		if err := json.Unmarshal(event.Data.Raw, &session); err == nil {
			golog.Info("📦 Session data parsed successfully", "sessionId", session.ID)
			// 1. Get User ID and Customer ID (from your original logic)
			uid := ""
			cusId := ""
			if session.Metadata != nil && session.Metadata["uid"] != "" {
				uid = session.Metadata["uid"]
			}
			if uid == "" && session.Customer != nil {
				user, _ := gomongo.Coll("rr", "users").FindOne(c.Request.Context(), bson.M{"stripeCusIds": session.Customer.ID}).DecodeBytes()
				if user != nil {
					uid, _ = user.Lookup("_id").StringValueOK()
				}
			}
			if session.Customer != nil {
				cusId = session.Customer.ID
			}

			// 2. Create user-customer mapping (from your original logic)
			if uid != "" && cusId != "" {
				usersColl := gomongo.Coll("rr", "users")
				filter := bson.M{"_id": uid}
				update := bson.M{"$addToSet": bson.M{"stripeCusIds": cusId}}
				_, _ = usersColl.UpdateOne(c.Request.Context(), filter, update)
			}

			// 3. 获取产品信息和续订判断（在插入订单之前进行）
			prdId := ""
			if v, ok := session.Metadata["prdId"]; ok && v != "" {
				prdId = v
			}

			// 4. 续订判断逻辑（在插入当前订单之前进行）
			isRenewal := false
			var subPlan models.SubPlan
			if uid != "" && prdId != "" {
				err := gomongo.Coll("rr", "subplan").FindOne(c.Request.Context(), bson.M{"prdId": prdId}).Decode(&subPlan)
				if err == nil {
					userSubColl := gomongo.Coll("rr", "usersub")

					// 正确的续订判断逻辑：
					// 续订 = 用户当前有活跃的订阅，这次是续费
					// 新订阅 = 用户当前没有活跃订阅（包括第一次订阅、取消后重新订阅）
					var activeSubscription bson.M
					err := userSubColl.FindOne(c.Request.Context(), bson.M{
						"uid": uid,
						"sts": "active", // 只查找当前活跃的订阅
					}).Decode(&activeSubscription)

					if err == nil {
						// 找到活跃订阅，这是续订
						isRenewal = true
						golog.Debug("Found active subscription, this is a renewal", "uid", uid, "activeSubId", activeSubscription["_id"])
					} else {
						// 没有活跃订阅，这是新订阅（包括第一次订阅或取消后重新订阅）
						isRenewal = false
						golog.Debug("No active subscription found, this is a new subscription", "uid", uid)
					}

					// Invalidate old subscriptions
					_, _ = userSubColl.UpdateMany(
						c.Request.Context(),
						bson.M{"uid": uid, "sts": "active"},
						bson.M{"$set": bson.M{"sts": "inactive", "mt": time.Now()}},
					)
					// Create new base subscription
					newUserSub := bson.M{
						"_id":         utils.GenerateNanoID(),
						"uid":         uid,
						"cusId":       cusId,
						"planId":      subPlan.PlanId, // Correctly uses the internal _id
						"stripeSubId": session.Subscription.ID,
						"sts":         "active",
						"ts":          time.Now(),
						"mt":          time.Now(),
					}
					_, _ = userSubColl.InsertOne(c.Request.Context(), newUserSub)

					// Create extra_report subscription record
					// 检查是否包含extra_report产品
					if extraReportProductId, ok := session.Metadata["extraReportProductId"]; ok && extraReportProductId != "" {
						extraReportSub := bson.M{
							"_id":         utils.GenerateNanoID(),
							"uid":         uid,
							"cusId":       cusId,
							"planId":      "extra_report_plan",     // 特殊标识
							"stripeSubId": session.Subscription.ID, // 同一个订阅ID
							"prdId":       extraReportProductId,
							"sts":         "active",
							"ts":          time.Now(),
							"mt":          time.Now(),
						}
						_, _ = userSubColl.InsertOne(c.Request.Context(), extraReportSub)

						golog.Info("Created extra_report subscription record for monthly plan",
							"uid", uid,
							"extraReportProductId", extraReportProductId,
							"stripeSubId", session.Subscription.ID)
					} else {
						// 检查是否为年费订阅
						golog.Info("Checking for annual subscription", "uid", uid, "metadata", session.Metadata)

						if billingType, ok := session.Metadata["billingType"]; ok && billingType == "annual" {
							// 年费订阅：为用户创建第二个独立的monthly extra_report订阅
							golog.Info("Creating secondary monthly extra_report subscription for annual user", "uid", uid)

							go func() {
								// 异步创建第二个订阅，避免阻塞webhook响应
								ctx := context.Background()
								if err := createSecondaryExtraReportSubscription(ctx, uid, cusId, session.Subscription.ID); err != nil {
									golog.Error("Failed to create secondary extra_report subscription",
										"error", err, "uid", uid, "primarySubId", session.Subscription.ID)
								}
							}()
						} else {
							golog.Info("Not an annual subscription or billingType not found",
								"uid", uid,
								"billingType", billingType,
								"hasMetadata", session.Metadata != nil)
						}
					}
				}
			}

			// 5. Create Order Info Record (在续订判断之后插入)
			ordColl := gomongo.Coll("rr", "ordinfo")
			ord := bson.M{
				"_id":    utils.GenerateNanoID(),
				"uid":    uid,
				"cusId":  cusId,
				"prdId":  prdId,
				"qty":    1,
				"ttlAmt": session.AmountTotal,
				"cur":    session.Currency,
				"sts":    session.Status,
				"ssnId":  session.ID,
				"ts":     time.Now(),
				"mt":     time.Now(),
			}
			_, _ = ordColl.InsertOne(c.Request.Context(), ord)

			// 订阅成功后，仅将 accountType 设为 vip，role 不变
			if uid != "" {
				usersColl := gomongo.Coll("rr", "users")
				_, _ = usersColl.UpdateOne(c.Request.Context(), bson.M{"_id": uid}, bson.M{"$set": bson.M{"acctTp": "vip", "mt": time.Now()}})
			}

			// 发送订阅成功邮件（只在checkout.session.completed中发送，避免重复）
			if uid != "" {
				if user, err := entities.GetUserByID(c.Request.Context(), uid); err == nil {
					userEmail := user.Email
					userName := user.Username
					if userEmail != "" {
						total := fmt.Sprintf("$%.2f", float64(session.AmountTotal)/100)

						// 获取计划名称
						planName := "Professional" // 默认值
						if subPlan.Nm != "" {
							planName = subPlan.Nm
						}

						golog.Debug("Sending subscription success email",
							"userEmail", userEmail,
							"userName", userName,
							"planName", planName,
							"isRenewal", isRenewal,
							"total", total)

						// 尝试获取invoice ID用于PDF附件
						// 在checkout.session.completed中，invoice可能还没有生成，所以这里可能找不到PDF
						// 但我们仍然尝试，如果找到就附加
						invoiceID := ""
						if session.Invoice != nil && session.Invoice.ID != "" {
							invoiceID = session.Invoice.ID
						}

						// 使用便捷函数发送邮件，如果找到PDF就自动附加
						if err := utils.SendSubscriptionEmailWithInvoicePDF(userEmail, userName, planName, total, invoiceID, isRenewal); err != nil {
							golog.Error("Failed to send subscription success email", "error", err, "userEmail", userEmail, "invoiceId", invoiceID)
						} else {
							golog.Info("Subscription success email sent successfully", "userEmail", userEmail, "invoiceId", invoiceID)
						}
					}
				}
			}
		}
	case "customer.subscription.created":
		// This event is redundant when using checkout sessions.
		// The main logic is handled in "checkout.session.completed" to ensure
		// we have access to the metadata (like prdId) for creating a correct usersub record.
		// Leaving this block empty prevents the creation of a duplicate, incomplete usersub record.
	case "invoice.paid":
		var inv stripe.Invoice
		if err := json.Unmarshal(event.Data.Raw, &inv); err == nil {
			uid := ""
			if inv.Metadata != nil && inv.Metadata["uid"] != "" {
				uid = inv.Metadata["uid"]
			}
			if uid == "" && inv.Customer != nil {
				user, _ := gomongo.Coll("rr", "users").FindOne(c.Request.Context(), bson.M{"stripeCusIds": inv.Customer.ID}).DecodeBytes()
				if user != nil {
					uid, _ = user.Lookup("_id").StringValueOK()
				}
			}

			pid := ""
			pmId := ""
			crdL4 := ""

			// 方法1: 尝试从 PaymentIntent 获取支付信息
			if inv.PaymentIntent != nil && inv.PaymentIntent.ID != "" {
				golog.Debug("Processing PaymentIntent", "paymentIntentID", inv.PaymentIntent.ID)
				pid = inv.PaymentIntent.ID

				pi, err := paymentintent.Get(inv.PaymentIntent.ID, nil)
				if err != nil {
					golog.Error("Failed to get PaymentIntent", "error", err, "paymentIntentID", inv.PaymentIntent.ID)
				} else {
					if pi.PaymentMethod != nil {
						pmId = pi.PaymentMethod.ID
						golog.Debug("Found payment method", "pmId", pmId)
					}
				}
			} else {
				golog.Debug("PaymentIntent is nil or empty")
			}

			// 方法2: 如果 PaymentIntent 为空，直接从 payinfo 表中查找用户的支付信息
			if pmId == "" && inv.Customer != nil {
				golog.Debug("Searching payinfo by customer ID", "customerID", inv.Customer.ID)
				payinfoColl := gomongo.Coll("rr", "payinfo")
				var payinfo map[string]interface{}
				if err := payinfoColl.FindOne(c.Request.Context(), bson.M{"cusId": inv.Customer.ID}).Decode(&payinfo); err == nil {
					if pm, ok := payinfo["pmId"].(string); ok {
						pmId = pm
						golog.Debug("Found payment method from payinfo", "pmId", pmId)
					}
					if l4, ok := payinfo["crdL4"].(string); ok {
						crdL4 = l4
						golog.Debug("Found card last 4 digits", "pmId", pmId, "crdL4", crdL4)
					}
				} else {
					golog.Debug("Payinfo not found", "customerID", inv.Customer.ID)
				}
			}

			// 方法3: 如果还是没有找到，尝试通过 pmId 查找 payinfo
			if pmId != "" && crdL4 == "" {
				golog.Debug("Searching payinfo by payment method ID", "pmId", pmId)
				payinfoColl := gomongo.Coll("rr", "payinfo")
				var payinfo map[string]interface{}
				if err := payinfoColl.FindOne(c.Request.Context(), bson.M{"pmId": pmId}).Decode(&payinfo); err == nil {
					if l4, ok := payinfo["crdL4"].(string); ok {
						crdL4 = l4
						golog.Debug("Found card last 4 digits by pmId", "pmId", pmId, "crdL4", crdL4)
					}
				} else {
					golog.Debug("Payinfo not found by pmId", "pmId", pmId)
				}
			}

			// 获取订阅ID
			subID := ""
			if inv.Subscription != nil && inv.Subscription.ID != "" {
				subID = inv.Subscription.ID
			}
			// 如果没有，尝试从 event.Data.Raw 里解析
			if subID == "" {
				var raw map[string]interface{}
				if err := json.Unmarshal(event.Data.Raw, &raw); err == nil {
					if lines, ok := raw["lines"].(map[string]interface{}); ok {
						if dataArr, ok := lines["data"].([]interface{}); ok && len(dataArr) > 0 {
							if item, ok := dataArr[0].(map[string]interface{}); ok {
								if parent, ok := item["parent"].(map[string]interface{}); ok {
									if sidetails, ok := parent["subscription_item_details"].(map[string]interface{}); ok {
										if sub, ok := sidetails["subscription"].(string); ok {
											subID = sub
										}
									}
									if subdetails, ok := parent["subscription_details"].(map[string]interface{}); ok {
										if sub, ok := subdetails["subscription"].(string); ok {
											subID = sub
										}
									}
								}
							}
						}
					}
				}
			}
			golog.Debug("Parsed subscription ID", "subID", subID)

			// 下载并存储Invoice PDF（异步处理，不阻塞webhook响应）
			invoicePdfPath := ""
			if inv.InvoicePDF != "" {
				go func() {
					if pdfPath, err := utils.DownloadAndStoreInvoicePDF(inv.ID, inv.InvoicePDF); err == nil {
						golog.Info("Invoice PDF downloaded and stored successfully",
							"invoiceId", inv.ID, "path", pdfPath)

						// 更新billhst记录，添加PDF路径
						billColl := gomongo.Coll("rr", "billhst")
						_, updateErr := billColl.UpdateOne(c.Request.Context(),
							bson.M{"invId": inv.ID},
							bson.M{"$set": bson.M{"invoicePdfPath": pdfPath, "mt": time.Now()}})
						if updateErr != nil {
							golog.Error("Failed to update billhst with PDF path",
								"error", updateErr, "invoiceId", inv.ID)
						}
					} else {
						golog.Error("Failed to download invoice PDF",
							"error", err, "invoiceId", inv.ID, "url", inv.InvoicePDF)
					}
				}()
			}

			billColl := gomongo.Coll("rr", "billhst")
			bill := models.BillHst{
				Bid:            utils.GenerateNanoID(),
				Uid:            uid,
				CusId:          inv.Customer.ID,
				Amt:            inv.AmountPaid,
				Cur:            string(inv.Currency),
				Sts:            string(inv.Status),
				Pid:            pid,
				PmId:           pmId,
				CrdL4:          crdL4,
				InvId:          inv.ID,
				InvoicePdfPath: invoicePdfPath, // 初始为空，异步更新
				Ts:             time.Now(),
				Mt:             time.Now(),
			}
			_, _ = billColl.InsertOne(c.Request.Context(), bill)

			// 检查是否为续订场景
			isRenewal := false
			if uid != "" && subID != "" {
				userSubColl := gomongo.Coll("rr", "usersub")
				count, err := userSubColl.CountDocuments(c.Request.Context(), bson.M{
					"uid": uid,
					"sts": "active",
				})
				if err == nil && count > 0 {
					isRenewal = true
					golog.Debug("Detected renewal scenario", "uid", uid, "subID", subID)

					// 续费时处理使用量报告
					go func() {
						ctx := context.Background()
						if err := processUsageReportingForRenewal(ctx, uid); err != nil {
							golog.Error("Failed to process usage reporting for renewal",
								"error", err, "uid", uid, "subID", subID)
						}
					}()
				}
			}

			// 对于续订场景，发送续订邮件
			if isRenewal && uid != "" {
				// 获取用户信息
				userColl := gomongo.Coll("rr", "users")
				var user map[string]interface{}
				if err := userColl.FindOne(c.Request.Context(), bson.M{"_id": uid}).Decode(&user); err == nil {
					if email, ok := user["email"].(string); ok && email != "" {
						golog.Debug("Sending subscription renewal email", "uid", uid, "email", email)

						// 发送续订邮件
						go func() {
							firstName := ""
							lastName := ""
							if name, ok := user["usrNm"].(string); ok {
								firstName = name
								// 如果需要分离姓和名，可以在这里处理
								// 目前假设 usrNm 是全名，lastName 留空
							}

							planName := "Professional" // 默认计划名称
							subscriptionCost := fmt.Sprintf("$%.2f", float64(inv.AmountPaid)/100)

							// 计算下次续订日期（假设为月度订阅）
							renewalDate := time.Now().AddDate(0, 1, 0)

							// 获取发票PDF URL
							invoicePDFURL := inv.InvoicePDF

							// 使用新的便捷函数，自动查找并附加Invoice PDF
							if err := utils.SendRenewalEmailWithInvoicePDF(email, firstName, lastName, planName, inv.ID, subscriptionCost, renewalDate, invoicePDFURL); err != nil {
								golog.Error("Failed to send renewal email with PDF attachment", "error", err, "uid", uid, "email", email, "invoiceId", inv.ID)
							} else {
								golog.Info("Renewal email sent successfully with automatic PDF attachment", "uid", uid, "email", email, "invoiceId", inv.ID)
							}
						}()
					}
				}
			} else {
				// 非续订场景，邮件已经在 checkout.session.completed 事件中处理
				golog.Debug("Invoice paid processed, email already sent in checkout.session.completed", "invoiceId", inv.ID, "uid", uid)
			}

			// 支付成功后，更新 usersub 状态为 active
			if subID != "" {
				userSubColl := gomongo.Coll("rr", "usersub")
				filter := bson.M{"stripeSubId": subID}
				update := bson.M{"$set": bson.M{"sts": "active", "mt": time.Now()}}
				res, err := userSubColl.UpdateOne(c.Request.Context(), filter, update)
				golog.Debug("Updated usersub to active", "subID", subID, "matched", res.MatchedCount, "modified", res.ModifiedCount, "error", err)
			}

		}
	case "invoice.payment_failed":
		var inv stripe.Invoice
		if err := json.Unmarshal(event.Data.Raw, &inv); err == nil {
			uid := ""
			if inv.Metadata != nil && inv.Metadata["uid"] != "" {
				uid = inv.Metadata["uid"]
			}
			if uid == "" && inv.Customer != nil {
				user, _ := gomongo.Coll("rr", "users").FindOne(c.Request.Context(), bson.M{"stripeCusIds": inv.Customer.ID}).DecodeBytes()
				if user != nil {
					uid, _ = user.Lookup("_id").StringValueOK()
				}
			}

			if uid != "" {
				// 获取用户信息
				userColl := gomongo.Coll("rr", "users")
				var user map[string]interface{}
				if err := userColl.FindOne(c.Request.Context(), bson.M{"_id": uid}).Decode(&user); err == nil {
					if email, ok := user["email"].(string); ok && email != "" {
						golog.Debug("Sending payment failed email", "uid", uid, "email", email)

						// 获取订阅计划名称
						planName := "Professional" // 默认计划名称

						// 尝试从订阅中获取计划名称
						if inv.Subscription != nil && inv.Subscription.ID != "" {
							userSubColl := gomongo.Coll("rr", "usersub")
							var userSub map[string]interface{}
							if err := userSubColl.FindOne(c.Request.Context(), bson.M{"stripeSubId": inv.Subscription.ID}).Decode(&userSub); err == nil {
								if prdId, ok := userSub["prdId"].(string); ok && prdId != "" {
									// 从 subplan 表获取计划名称
									subplanColl := gomongo.Coll("rr", "subplan")
									var subplan map[string]interface{}
									if err := subplanColl.FindOne(c.Request.Context(), bson.M{"_id": prdId}).Decode(&subplan); err == nil {
										if nm, ok := subplan["nm"].(string); ok && nm != "" {
											planName = nm
										}
									}
								}
							}
						}

						// 发送支付失败邮件
						go func() {
							firstName := ""
							if name, ok := user["usrNm"].(string); ok {
								firstName = name
							}

							if err := utils.SendPaymentFailedEmail(email, firstName, planName); err != nil {
								golog.Error("Failed to send payment failed email", "error", err, "uid", uid, "email", email)
							} else {
								golog.Debug("Payment failed email sent successfully", "uid", uid, "email", email)
							}
						}()
					}
				}
			}

			golog.Debug("Invoice payment failed processed", "invoiceId", inv.ID, "uid", uid)
		}
	case "payment_method.attached":
		var pm stripe.PaymentMethod
		if err := json.Unmarshal(event.Data.Raw, &pm); err == nil {
			uid := ""
			if pm.Metadata != nil && pm.Metadata["uid"] != "" {
				uid = pm.Metadata["uid"]
			}
			if uid == "" && pm.Customer != nil {
				user, _ := gomongo.Coll("rr", "users").FindOne(c.Request.Context(), bson.M{"stripeCusIds": pm.Customer.ID}).DecodeBytes()
				if user != nil {
					uid, _ = user.Lookup("_id").StringValueOK()
				}
			}
			payColl := gomongo.Coll("rr", "payinfo")
			if pm.Card != nil {
				pay := bson.M{
					"_id":    utils.GenerateNanoID(),
					"uid":    uid,
					"cusId":  pm.Customer.ID,
					"pmId":   pm.ID,
					"crdBrd": pm.Card.Brand,
					"crdL4":  pm.Card.Last4,
					"expMn":  pm.Card.ExpMonth,
					"expYr":  pm.Card.ExpYear,
					"mtdTp":  pm.Type,
					"ts":     time.Now(),
				}

				// 添加账单地址信息
				if pm.BillingDetails != nil && pm.BillingDetails.Address != nil {
					pay["billingAddress"] = bson.M{
						"line1":      pm.BillingDetails.Address.Line1,
						"line2":      pm.BillingDetails.Address.Line2,
						"city":       pm.BillingDetails.Address.City,
						"state":      pm.BillingDetails.Address.State,
						"postalCode": pm.BillingDetails.Address.PostalCode,
						"country":    pm.BillingDetails.Address.Country,
					}

					// 注释掉自动更新用户地址的逻辑，避免覆盖用户在Account Settings中设置的地址
					// Billing Address只保存在payinfo中用于显示，不影响用户的address字段
					// if uid != "" {
					// 	usersColl := gomongo.Coll("rr", "users")
					// 	userAddress := bson.M{
					// 		"street":  pm.BillingDetails.Address.Line1,
					// 		"unit":    pm.BillingDetails.Address.Line2,
					// 		"city":    pm.BillingDetails.Address.City,
					// 		"prov":    pm.BillingDetails.Address.State,
					// 		"country": pm.BillingDetails.Address.Country,
					// 		"zipCode": pm.BillingDetails.Address.PostalCode,
					// 	}
					// 	filter := bson.M{"_id": uid}
					// 	update := bson.M{"$set": bson.M{"address": userAddress}}
					// 	_, _ = usersColl.UpdateOne(c.Request.Context(), filter, update)
					// }
				}

				_, _ = payColl.InsertOne(c.Request.Context(), pay)
			}
		}
	case "customer.subscription.deleted":
		var sub stripe.Subscription
		if err := json.Unmarshal(event.Data.Raw, &sub); err == nil {
			// 取消订阅后，仅将 accountType 设为空，role 不变
			customerId := ""
			if sub.Customer != nil {
				customerId = sub.Customer.ID
			}
			if customerId != "" {
				usersColl := gomongo.Coll("rr", "users")
				_, _ = usersColl.UpdateOne(c.Request.Context(), bson.M{"stripeCusIds": customerId}, bson.M{"$set": bson.M{"acctTp": "", "mt": time.Now()}})
				var userDoc bson.M
				if err := usersColl.FindOne(c.Request.Context(), bson.M{"stripeCusIds": customerId}).Decode(&userDoc); err == nil {
					if uid, ok := userDoc["_id"].(string); ok && uid != "" {
						// 发送订阅取消确认邮件（统一在webhook中发送，避免重复）
						if user, err := entities.GetUserByID(c.Request.Context(), uid); err == nil {
							userEmail := user.Email
							userName := user.Username
							if userEmail != "" {
								// 获取计划名称
								planName := "Professional" // 默认值

								// 尝试从用户订阅记录中获取计划信息
								userSubColl := gomongo.Coll("rr", "usersub")
								var userSub bson.M
								if err := userSubColl.FindOne(c.Request.Context(), bson.M{
									"uid":         uid,
									"stripeSubId": sub.ID,
								}).Decode(&userSub); err == nil {
									if planId, ok := userSub["planId"].(string); ok && planId != "" {
										var subPlan models.SubPlan
										if err := gomongo.Coll("rr", "subplan").FindOne(c.Request.Context(), bson.M{"_id": planId}).Decode(&subPlan); err == nil {
											if subPlan.Nm != "" {
												planName = subPlan.Nm
											}
										}
									}
								}

								// 取消日期
								cancellationDate := time.Now().Format("January 2, 2006")

								golog.Debug("Sending subscription cancelled email (webhook)",
									"userEmail", userEmail,
									"userName", userName,
									"planName", planName,
									"cancellationDate", cancellationDate)
								_ = utils.SendSubscriptionCancelledEmail(userEmail, userName, planName, cancellationDate)
							}
						}

						// 处理双订阅模式：如果取消的是主订阅，也要取消相关的extra_report订阅
						err := handleDualSubscriptionCancellation(c.Request.Context(), uid, sub.ID)
						if err != nil {
							golog.Error("Failed to handle dual subscription cancellation", "error", err, "userId", uid, "subId", sub.ID)
						}

						// 处理租约的 rent reporting 关闭和邮件通知
						err = handleSubscriptionCancellationRentReporting(c.Request.Context(), uid)
						if err != nil {
							golog.Error("Failed to handle rent reporting cancellation", "error", err, "userId", uid)
						}
					}
				}
			}

			// 同步更新相关订单状态
			ordColl := gomongo.Coll("rr", "ordinfo")
			ordFilter := bson.M{}
			// 优先用 metadata 里的 uid/prdId
			if sub.Metadata != nil && sub.Metadata["uid"] != "" && sub.Metadata["prdId"] != "" {
				ordFilter["uid"] = sub.Metadata["uid"]
				ordFilter["prdId"] = sub.Metadata["prdId"]
			} else {
				// 若 metadata 不全，尝试通过 customer id 反查 usersub
				ordFilter["cusId"] = sub.Customer.ID
			}
			ordFilter["sts"] = bson.M{"$ne": "canceled"}
			ordUpdate := bson.M{"$set": bson.M{"sts": "canceled", "mt": time.Now()}}
			_, _ = ordColl.UpdateMany(c.Request.Context(), ordFilter, ordUpdate)
		}
	}

	c.Status(200)
}

// processUsageReportingForRenewal 处理续费时的使用量报告
func processUsageReportingForRenewal(ctx context.Context, userID string) error {
	golog.Info("Processing usage reporting for renewal", "uid", userID)

	// 检查用户是否有extra_report订阅（月费或年费用户的月费extra_report）
	userSubColl := gomongo.Coll("rr", "usersub")
	var extraReportSub bson.M

	// 首先尝试查找月费extra_report订阅
	err := userSubColl.FindOne(ctx, bson.M{
		"uid":    userID,
		"planId": "extra_report_plan",
		"sts":    "active",
	}).Decode(&extraReportSub)

	// 如果没有找到月费订阅，尝试查找年费用户的月费extra_report订阅
	if err != nil {
		err = userSubColl.FindOne(ctx, bson.M{
			"uid":    userID,
			"planId": "extra_report_plan_monthly", // 年费用户的月费extra_report
			"sts":    "active",
		}).Decode(&extraReportSub)

		if err != nil {
			golog.Debug("No extra_report subscription found for user", "uid", userID)
			return nil // 用户没有extra_report订阅，跳过
		}
		golog.Info("Found monthly extra_report subscription for annual user", "uid", userID)
	} else {
		golog.Info("Found monthly extra_report subscription for monthly user", "uid", userID)
	}

	// 获取上个月的年月（Metro2报告基于上个月数据）
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)
	year := lastMonth.Year()
	month := int(lastMonth.Month())

	golog.Info("Calculating usage for renewal", "uid", userID, "year", year, "month", month)

	// 从Metro2日志计算使用量
	usageCount, err := entities.CalculateUsageFromMetro2Logs(ctx, userID, year, month)
	if err != nil {
		return fmt.Errorf("failed to calculate usage from Metro2 logs: %v", err)
	}

	// 获取基础配额
	baseQuota := getBaseQuotaForUsage()

	golog.Info("Usage calculation result",
		"uid", userID,
		"usageCount", usageCount,
		"baseQuota", baseQuota)

	// 如果没有超量使用，无需报告
	if usageCount <= baseQuota {
		golog.Info("No overage usage, skipping Stripe reporting", "uid", userID)
		return nil
	}

	// 计算超量数量
	overageCount := usageCount - baseQuota

	// 所有用户（月费和年费）都使用metered billing报告超量使用
	// 年费用户现在有独立的月费extra_report订阅，可以正常使用metered billing
	usageService := services.NewMonthlyUsageReportService()
	err = reportUsageToStripeForUser(usageService, ctx, userID, overageCount, year, month)
	if err != nil {
		return fmt.Errorf("failed to report usage to Stripe: %v", err)
	}

	golog.Info("Successfully reported usage to Stripe for renewal",
		"uid", userID,
		"overageCount", overageCount,
		"year", year,
		"month", month)

	return nil
}

// getBaseQuotaForUsage 获取基础配额（从配置文件）
func getBaseQuotaForUsage() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 3 // 默认值：3个租约
}

// reportUsageToStripeForUser 为单个用户向Stripe报告使用量
func reportUsageToStripeForUser(service *services.MonthlyUsageReportService, ctx context.Context, userID string, overageCount int, year, month int) error {
	return service.ReportSingleUserUsage(ctx, userID, overageCount, year, month)
}

// handleSubscriptionCancellationRentReporting 处理订阅取消时的租约 rent reporting 关闭和邮件通知
func handleSubscriptionCancellationRentReporting(ctx context.Context, userID string) error {
	leasesColl := gomongo.Coll("rr", "leases")
	if leasesColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 查找该用户所有启用了 rent reporting 的租约
	filter := bson.M{
		"usrId":   userID,
		"rentRep": true,
	}

	cursor, err := leasesColl.Find(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to find leases: %v", err)
	}
	defer cursor.Close(ctx)

	var leasesToUpdate []entities.Lease
	if err = cursor.All(ctx, &leasesToUpdate); err != nil {
		return fmt.Errorf("failed to decode leases: %v", err)
	}

	// 批量更新租约状态
	if len(leasesToUpdate) > 0 {
		updateFilter := bson.M{"usrId": userID, "rentRep": true}
		update := bson.M{"$set": bson.M{"rentRep": false, "mt": time.Now()}}
		res, err := leasesColl.UpdateMany(ctx, updateFilter, update)
		if err != nil {
			return fmt.Errorf("failed to update leases: %v", err)
		}
		golog.Debug("Updated leases rentRep status", "matched", res.MatchedCount, "modified", res.ModifiedCount)

		// 为每个租约发送暂停邮件通知
		for _, originalLease := range leasesToUpdate {
			// 创建更新后的租约对象
			updatedLease := originalLease
			updatedLease.RentReporting = false

			// 发送租金报告暂停通知邮件
			err = sendRentReportingPausedNotificationForLease(ctx, &originalLease, &updatedLease)
			if err != nil {
				// 记录错误但不影响主要操作
				golog.Error("Failed to send rent reporting paused notification emails", "error", err, "leaseId", originalLease.ID)
			}
		}
	}

	return nil
}

// sendRentReportingPausedNotificationForLease 为单个租约发送租金报告暂停通知
func sendRentReportingPausedNotificationForLease(ctx context.Context, originalLease, updatedLease *entities.Lease) error {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get landlord user info", "error", err, "userId", updatedLease.UserID)
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetProperty(ctx, updatedLease.PropertyID, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", updatedLease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备租户邮件信息
	var tenants []utils.TenantEmailInfo
	for _, tenant := range originalLease.CurrentTenants {
		if tenant.Email != "" && tenant.FirstName != "" {
			tenants = append(tenants, utils.TenantEmailInfo{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			})
		}
	}

	if len(tenants) > 0 {
		// 异步发送暂停邮件
		utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting paused notification emails (subscription cancelled)",
			"leaseId", updatedLease.ID,
			"tenantCount", len(tenants),
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", updatedLease.PropertyID)
	} else {
		golog.Warn("No valid tenant emails found for rent reporting paused notification", "leaseId", updatedLease.ID)
	}

	return nil
}

// createSecondaryExtraReportSubscription 为年费用户创建第二个monthly extra_report订阅
func createSecondaryExtraReportSubscription(ctx context.Context, userID, customerID, primarySubID string) error {
	golog.Info("🚀 Starting createSecondaryExtraReportSubscription",
		"uid", userID,
		"cusId", customerID,
		"primarySubId", primarySubID)

	// 1. 获取extra_report价格ID
	extraReportPriceId := getExtraReportPriceIdFromConfig()
	if extraReportPriceId == "" {
		return fmt.Errorf("extra_report_price_id not configured")
	}

	// 2. 创建Stripe订阅
	golog.Info("Creating actual Stripe subscription for annual user",
		"customerID", customerID,
		"priceId", extraReportPriceId,
		"primarySubId", primarySubID)

	// 使用简单的HTTP请求调用Stripe API
	subscriptionID, err := createStripeSubscriptionViaHTTP(customerID, extraReportPriceId, userID, primarySubID)
	if err != nil {
		return fmt.Errorf("failed to create Stripe subscription: %v", err)
	}

	golog.Info("Successfully created Stripe subscription",
		"customerID", customerID,
		"subscriptionID", subscriptionID,
		"priceId", extraReportPriceId,
		"primarySubId", primarySubID)

	// 3. 在数据库中创建usersub记录
	userSubColl := gomongo.Coll("rr", "usersub")
	extraReportSub := bson.M{
		"_id":          utils.GenerateNanoID(),
		"uid":          userID,
		"cusId":        customerID,
		"planId":       "extra_report_plan_monthly", // 月费extra_report标识
		"stripeSubId":  subscriptionID,              // 使用实际的subscription.ID
		"prdId":        getExtraReportProductId(),
		"sts":          "active",
		"billingType":  "monthly_for_annual",
		"primarySubId": primarySubID, // 关联到主订阅
		"ts":           time.Now(),
		"mt":           time.Now(),
	}
	_, err = userSubColl.InsertOne(ctx, extraReportSub)
	if err != nil {
		return fmt.Errorf("failed to create usersub record: %v", err)
	}

	golog.Info("Created secondary extra_report subscription record",
		"uid", userID,
		"subscriptionID", subscriptionID,
		"primarySubId", primarySubID)

	return nil
}

// getExtraReportPriceIdFromConfig 从配置文件获取extra_report价格ID
func getExtraReportPriceIdFromConfig() string {
	if priceId := goconfig.Config("stripe.extra_report_price_id"); priceId != nil {
		if priceIdStr, ok := priceId.(string); ok && priceIdStr != "" {
			return priceIdStr
		}
	}
	return "price_1Rv1WDRdRW2qyPyrOIELKvfs" // 默认值
}

// createStripeSubscriptionViaHTTP 通过HTTP API创建Stripe订阅
func createStripeSubscriptionViaHTTP(customerID, priceID, userID, primarySubID string) (string, error) {
	golog.Info("Creating Stripe subscription via HTTP API",
		"customerID", customerID,
		"priceID", priceID,
		"userID", userID,
		"primarySubID", primarySubID)

	// 获取Stripe API密钥
	apiKey := ""
	if key := goconfig.Config("stripe.secret_key"); key != nil {
		if keyStr, ok := key.(string); ok {
			apiKey = keyStr
		}
	}
	if apiKey == "" {
		return "", fmt.Errorf("stripe secret key not configured")
	}

	// 准备请求数据
	data := fmt.Sprintf("customer=%s&items[0][price]=%s", customerID, priceID)
	data += fmt.Sprintf("&metadata[uid]=%s", userID)
	data += fmt.Sprintf("&metadata[type]=%s", "extra_report_for_annual")
	data += fmt.Sprintf("&metadata[primarySubId]=%s", primarySubID)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "https://api.stripe.com/v1/subscriptions", strings.NewReader(data))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("stripe API error (status %d): %s", resp.StatusCode, string(body))
	}

	// 解析响应获取订阅ID
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse response: %v", err)
	}

	subscriptionID, ok := result["id"].(string)
	if !ok {
		return "", fmt.Errorf("subscription ID not found in response")
	}

	golog.Info("Successfully created Stripe subscription via HTTP",
		"subscriptionID", subscriptionID,
		"customerID", customerID)

	return subscriptionID, nil
}

// handleDualSubscriptionCancellation 处理双订阅模式的取消逻辑
func handleDualSubscriptionCancellation(ctx context.Context, userID, cancelledSubID string) error {
	golog.Info("Handling dual subscription cancellation", "uid", userID, "cancelledSubId", cancelledSubID)

	userSubColl := gomongo.Coll("rr", "usersub")
	if userSubColl == nil {
		return fmt.Errorf("usersub collection not initialized")
	}

	// 1. 首先检查订阅类型，确定取消策略
	// 查找所有使用该stripeSubId的订阅
	cursor, err := userSubColl.Find(ctx, bson.M{"stripeSubId": cancelledSubID})
	if err != nil {
		return fmt.Errorf("failed to find subscriptions with subId %s: %v", cancelledSubID, err)
	}
	defer cursor.Close(ctx)

	var allSubs []bson.M
	if err = cursor.All(ctx, &allSubs); err != nil {
		return fmt.Errorf("failed to decode subscriptions: %v", err)
	}

	golog.Info("Found subscriptions to cancel", "subId", cancelledSubID, "count", len(allSubs))

	// 2. 判断订阅模式
	isMonthlyMode := false
	isAnnualMode := false

	for _, sub := range allSubs {
		planId, _ := sub["planId"].(string)
		billingType, _ := sub["billingType"].(string)

		// 年费模式：有billingType为monthly_for_annual的订阅，或者有primarySubId字段
		if billingType == "monthly_for_annual" || sub["primarySubId"] != nil {
			isAnnualMode = true
		}

		// 月费模式：planId为extra_report_plan，或者billingType为monthly且不是年费用户的extra_report
		if planId == "extra_report_plan" || (billingType == "monthly" && planId != "extra_report_plan_monthly") {
			isMonthlyMode = true
		}

		// 年费主订阅：billingType为annual
		if billingType == "annual" {
			isAnnualMode = true
		}
	}

	golog.Info("Subscription mode detected", "monthly", isMonthlyMode, "annual", isAnnualMode)

	// 3. 根据模式执行不同的取消策略
	if isMonthlyMode && !isAnnualMode {
		// 月费模式：取消所有使用该stripeSubId的订阅
		golog.Info("Processing monthly subscription cancellation")
		filter := bson.M{"stripeSubId": cancelledSubID}
		update := bson.M{"$set": bson.M{"sts": "inactive", "mt": time.Now()}}
		res, err := userSubColl.UpdateMany(ctx, filter, update)
		if err != nil {
			return fmt.Errorf("failed to update monthly subscriptions: %v", err)
		}
		golog.Info("Updated monthly subscriptions", "subId", cancelledSubID, "modified", res.ModifiedCount)

	} else {
		// 年费模式或混合模式：使用原有逻辑
		golog.Info("Processing annual subscription cancellation")

		// 3a. 标记被取消的订阅为inactive
		filter := bson.M{"stripeSubId": cancelledSubID}
		update := bson.M{"$set": bson.M{"sts": "inactive", "mt": time.Now()}}
		res, err := userSubColl.UpdateOne(ctx, filter, update)
		if err != nil {
			return fmt.Errorf("failed to update cancelled subscription: %v", err)
		}
		golog.Debug("Updated cancelled subscription", "subId", cancelledSubID, "matched", res.MatchedCount)

		// 3b. 检查是否为年费用户的主订阅被取消
		// 如果是，需要同时取消相关的extra_report订阅
		var cancelledSub bson.M
		err = userSubColl.FindOne(ctx, bson.M{"stripeSubId": cancelledSubID}).Decode(&cancelledSub)
		if err == nil {
			golog.Info("Found cancelled subscription", "subId", cancelledSubID, "planId", cancelledSub["planId"])

			// 查找需要取消的关联extra_report订阅
			extraReportFilter := bson.M{
				"uid":          userID,
				"planId":       "extra_report_plan_monthly",
				"primarySubId": cancelledSubID, // 关联到被取消的主订阅
				"sts":          "active",
			}

			// 先查找所有需要取消的extra_report订阅
			extraCursor, err := userSubColl.Find(ctx, extraReportFilter)
			if err != nil {
				golog.Error("Failed to find extra_report subscriptions", "error", err)
			} else {
				defer extraCursor.Close(ctx)

				var extraSubs []bson.M
				if err = extraCursor.All(ctx, &extraSubs); err == nil {
					golog.Info("Found extra_report subscriptions to cancel", "count", len(extraSubs))

					// 取消每个extra_report订阅
					for _, extraSub := range extraSubs {
						extraStripeSubId, _ := extraSub["stripeSubId"].(string)
						if extraStripeSubId != "" {
							golog.Info("Cancelling extra_report subscription in Stripe",
								"extraSubId", extraStripeSubId,
								"primarySubId", cancelledSubID)

							// 调用Stripe API取消订阅
							err := cancelStripeSubscription(extraStripeSubId)
							if err != nil {
								golog.Error("Failed to cancel extra_report subscription in Stripe",
									"error", err,
									"extraSubId", extraStripeSubId)
							} else {
								golog.Info("Successfully cancelled extra_report subscription in Stripe",
									"extraSubId", extraStripeSubId)
							}
						}
					}
				}
			}

			// 更新数据库中的extra_report订阅状态
			extraReportUpdate := bson.M{"$set": bson.M{"sts": "inactive", "mt": time.Now()}}
			extraRes, err := userSubColl.UpdateMany(ctx, extraReportFilter, extraReportUpdate)
			if err != nil {
				golog.Error("Failed to update related extra_report subscriptions", "error", err)
			} else if extraRes.ModifiedCount > 0 {
				golog.Info("Updated related extra_report subscriptions in database",
					"uid", userID,
					"primarySubId", cancelledSubID,
					"modified", extraRes.ModifiedCount)
			} else {
				golog.Info("No extra_report subscriptions found to update",
					"uid", userID,
					"primarySubId", cancelledSubID)
			}
		} else {
			golog.Error("Failed to find cancelled subscription", "error", err, "subId", cancelledSubID)
		}
	}

	// 3. 反向检查：如果取消的是extra_report订阅，不需要特殊处理
	// 因为extra_report订阅是独立的，不会影响主订阅

	return nil
}

// cancelStripeSubscription 通过HTTP API取消Stripe订阅
func cancelStripeSubscription(subscriptionID string) error {
	golog.Info("Cancelling Stripe subscription via HTTP API", "subscriptionID", subscriptionID)

	// 获取Stripe API密钥
	apiKey := ""
	if key := goconfig.Config("stripe.secret_key"); key != nil {
		if keyStr, ok := key.(string); ok {
			apiKey = keyStr
		}
	}
	if apiKey == "" {
		return fmt.Errorf("stripe secret key not configured")
	}

	// 创建HTTP请求
	url := fmt.Sprintf("https://api.stripe.com/v1/subscriptions/%s", subscriptionID)
	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != 200 {
		return fmt.Errorf("stripe API error (status %d): %s", resp.StatusCode, string(body))
	}

	golog.Info("Successfully cancelled Stripe subscription via HTTP", "subscriptionID", subscriptionID)
	return nil
}
