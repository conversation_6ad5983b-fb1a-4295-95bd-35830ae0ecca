package controller

import (
	"fmt"
	"net/http"
	"rent_report/entities"
	"rent_report/models"
	"rent_report/router"
	"rent_report/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

type RentReportUsageController struct{}

func init() {
	router.Register(&RentReportUsageController{})
}

// getBaseQuota 从配置文件获取基础配额
func getBaseQuota() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 20 // 默认值
}

// getOverageRate 从配置文件获取超量费率
func getOverageRate() int {
	if rate := goconfig.Config("rentReportUsage.overageRateCAD"); rate != nil {
		if rateInt, ok := rate.(int); ok {
			return rateInt
		}
		if rateInt64, ok := rate.(int64); ok {
			return int(rateInt64)
		}
	}
	return 100 // 默认值：$1.00 CAD = 100分
}

func (c *RentReportUsageController) RegisterRoutes(r *gin.Engine) {
	usageRouter := r.Group("/v1/rent-reports/usage")
	{
		// 获取当前使用量统计
		usageRouter.GET("/current", c.handleGetCurrentUsage)

		// 检查使用量限制
		usageRouter.GET("/check-limit", c.handleCheckUsageLimit)

		// 获取使用量历史
		usageRouter.GET("/history", c.handleGetUsageHistory)

		// 手动更新使用量（测试用）
		usageRouter.POST("/update", c.handleUpdateUsage)

		// 确认超量费用
		usageRouter.POST("/confirm-overage", c.handleConfirmOverage)
	}
}

// handleGetCurrentUsage 获取当前使用量统计
func (c *RentReportUsageController) handleGetCurrentUsage(ctx *gin.Context) {
	// 获取当前用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || userID == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "未登录或token无效"})
		return
	}

	// 从Metro2日志获取使用量统计
	stats, err := entities.GetUsageStatisticsFromMetro2(ctx.Request.Context(), userID)
	if err != nil {
		golog.Error("Failed to get usage statistics from Metro2", "error", err, "userID", userID)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "获取使用量统计失败"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// handleCheckUsageLimit 检查使用量限制
func (c *RentReportUsageController) handleCheckUsageLimit(ctx *gin.Context) {
	// 获取当前用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || userID == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "未登录或token无效"})
		return
	}

	// 检查使用量限制
	canEnable, stats, err := entities.CheckUsageLimit(ctx.Request.Context(), userID)
	if err != nil {
		golog.Error("Failed to check usage limit", "error", err, "userID", userID)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "检查使用量限制失败"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":   true,
		"canEnable": canEnable,
		"stats":     stats,
		"message":   getUsageLimitMessage(canEnable, stats),
	})
}

// handleGetUsageHistory 获取使用量历史
func (c *RentReportUsageController) handleGetUsageHistory(ctx *gin.Context) {
	// 获取当前用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || userID == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "未登录或token无效"})
		return
	}

	// 获取limit参数，默认12个月
	limitStr := ctx.DefaultQuery("limit", "12")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 12
	}

	// 获取使用量历史
	history, err := entities.GetUsageHistory(ctx.Request.Context(), userID, limit)
	if err != nil {
		golog.Error("Failed to get usage history", "error", err, "userID", userID)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "获取使用量历史失败"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    history,
	})
}

// handleUpdateUsage 手动更新使用量（测试用）
func (c *RentReportUsageController) handleUpdateUsage(ctx *gin.Context) {
	// 获取当前用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || userID == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "未登录或token无效"})
		return
	}

	// 解析请求参数
	var req struct {
		UsedCount int `json:"usedCount" binding:"required,min=0"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	// 更新使用量
	err = entities.UpdateUsageCount(ctx.Request.Context(), userID, req.UsedCount)
	if err != nil {
		golog.Error("Failed to update usage count", "error", err, "userID", userID, "usedCount", req.UsedCount)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "更新使用量失败"})
		return
	}

	// 记录操作日志
	baseQuota := getBaseQuota()
	overageRate := getOverageRate()
	isOverage := req.UsedCount > baseQuota
	chargeAmount := int64(0)
	if isOverage {
		overageCount := req.UsedCount - baseQuota
		chargeAmount = int64(overageCount) * int64(overageRate)
	}

	err = entities.LogUsageOperation(ctx.Request.Context(), userID, "manual_update", "manual_update", isOverage, chargeAmount)
	if err != nil {
		golog.Warn("Failed to log usage operation", "error", err, "userID", userID)
	}

	// 获取更新后的统计信息
	stats, err := entities.GetUsageStatistics(ctx.Request.Context(), userID)
	if err != nil {
		golog.Error("Failed to get updated statistics", "error", err, "userID", userID)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "获取更新后统计信息失败"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "使用量更新成功",
		"data":    stats,
	})
}

// handleConfirmOverage 确认超量费用
func (c *RentReportUsageController) handleConfirmOverage(ctx *gin.Context) {
	// 获取当前用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || userID == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "未登录或token无效"})
		return
	}

	// 解析请求参数
	var req struct {
		LeaseID string `json:"leaseId" binding:"required"`
		Confirm bool   `json:"confirm" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	if !req.Confirm {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "用户未确认超量费用"})
		return
	}

	// 检查当前使用量状态
	canEnable, stats, err := entities.CheckUsageLimit(ctx.Request.Context(), userID)
	if err != nil {
		golog.Error("Failed to check usage limit", "error", err, "userID", userID)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "检查使用量限制失败"})
		return
	}

	// 如果在配额内，直接允许
	if canEnable {
		ctx.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "在配额内，无需额外费用",
			"stats":   stats,
		})
		return
	}

	// 超出配额，计算费用并记录确认
	chargeAmount := getOverageRate() // 从配置文件获取

	err = entities.LogUsageOperation(ctx.Request.Context(), userID, req.LeaseID, "confirm_overage", true, int64(chargeAmount))
	if err != nil {
		golog.Error("Failed to log overage confirmation", "error", err, "userID", userID, "leaseID", req.LeaseID)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "记录超量确认失败"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "超量费用确认成功",
		"chargeAmount": float64(chargeAmount) / 100, // 转换为元
		"currency":     "CAD",
		"stats":        stats,
	})
}

// getUsageLimitMessage 获取使用量限制消息
func getUsageLimitMessage(canEnable bool, stats *models.UsageStatistics) string {
	if canEnable {
		return fmt.Sprintf("剩余配额：%d/%d", stats.RemainingQuota, stats.BaseQuota)
	}

	overageCharge := float64(getOverageRate()) / 100
	return fmt.Sprintf("已超出配额，启用新报告将产生 $%.2f %s 的额外费用", overageCharge, stats.Currency)
}
