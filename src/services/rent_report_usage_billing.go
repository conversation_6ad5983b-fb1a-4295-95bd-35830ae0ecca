package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/sub"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MonthlyUsageReportService 月度使用量报告服务
type MonthlyUsageReportService struct {
	ExtraReportProductId string
	ExtraReportPriceId   string
}

// NewMonthlyUsageReportService 创建月度使用量报告服务
func NewMonthlyUsageReportService() *MonthlyUsageReportService {
	// 从配置文件读取Stripe产品ID
	extraReportProductId := "prod_SqeFaz9P1fDdOq" // 默认值
	if configProductId := goconfig.Config("stripe.extra_report_product_id"); configProductId != nil {
		if productIdStr, ok := configProductId.(string); ok && productIdStr != "" {
			extraReportProductId = productIdStr
		}
	}

	extraReportPriceId := "price_1RuwwtRdRW2qyPyrLm0nLLG0" // 默认值
	if configPriceId := goconfig.Config("stripe.extra_report_price_id"); configPriceId != nil {
		if priceIdStr, ok := configPriceId.(string); ok && priceIdStr != "" {
			extraReportPriceId = priceIdStr
		}
	}

	return &MonthlyUsageReportService{
		ExtraReportProductId: extraReportProductId,
		ExtraReportPriceId:   extraReportPriceId,
	}
}

// getBaseQuota 从配置文件获取基础配额
func getBaseQuota() int {
	if quota := goconfig.Config("rentReportUsage.baseQuota"); quota != nil {
		if quotaInt, ok := quota.(int); ok {
			return quotaInt
		}
		if quotaInt64, ok := quota.(int64); ok {
			return int(quotaInt64)
		}
	}
	return 20 // 默认值
}

// getStripeSecretKey 从配置文件获取Stripe密钥
func getStripeSecretKey() string {
	if key := goconfig.Config("stripe.secret_key"); key != nil {
		if keyStr, ok := key.(string); ok && keyStr != "" {
			return keyStr
		}
	}
	return ""
}

// ProcessMonthlyUsageReporting 处理月度使用量报告
func (s *MonthlyUsageReportService) ProcessMonthlyUsageReporting(ctx context.Context) error {
	golog.Info("Starting monthly usage reporting process")

	// 获取上个月的年月
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)
	year := lastMonth.Year()
	month := int(lastMonth.Month())

	golog.Info("Processing usage for month", "year", year, "month", month)

	// 获取所有VIP用户的使用量
	userUsageMap, err := s.calculateAllUserUsageFromMetro2(ctx, year, month)
	if err != nil {
		return fmt.Errorf("failed to calculate user usage: %v", err)
	}

	golog.Info("Found user usage records", "userCount", len(userUsageMap))

	successCount := 0
	errorCount := 0

	baseQuota := getBaseQuota()

	// 为每个有超量使用的用户报告到Stripe
	for userID, usageCount := range userUsageMap {
		if usageCount > baseQuota {
			overageCount := usageCount - baseQuota
			err := s.reportUsageToStripe(ctx, userID, overageCount, year, month)
			if err != nil {
				golog.Error("Failed to report usage to Stripe",
					"error", err,
					"uid", userID,
					"overageCount", overageCount)
				errorCount++
			} else {
				golog.Info("Successfully reported usage to Stripe",
					"uid", userID,
					"totalUsage", usageCount,
					"overageCount", overageCount)
				successCount++
			}
		} else {
			golog.Debug("No overage usage for user", "uid", userID, "usage", usageCount)
		}
	}

	golog.Info("Monthly usage reporting completed",
		"successCount", successCount,
		"errorCount", errorCount,
		"totalUsers", len(userUsageMap))

	return nil
}

// calculateAllUserUsageFromMetro2 从Metro2日志计算所有用户的使用量
func (s *MonthlyUsageReportService) calculateAllUserUsageFromMetro2(ctx context.Context, year, month int) (map[string]int, error) {
	reportMonth := fmt.Sprintf("%04d-%02d", year, month)

	// 获取该月份最新的Metro2生成记录
	coll := gomongo.Coll("rr", "metro2_generation_logs")
	if coll == nil {
		return nil, fmt.Errorf("metro2_generation_logs collection not initialized")
	}

	cursor, err := coll.Find(ctx, bson.M{"reportMonth": reportMonth})
	if err != nil {
		return nil, fmt.Errorf("failed to query metro2 generation logs: %v", err)
	}
	defer cursor.Close(ctx)

	var allLogs []bson.M
	if err = cursor.All(ctx, &allLogs); err != nil {
		return nil, fmt.Errorf("failed to decode metro2 generation logs: %v", err)
	}

	if len(allLogs) == 0 {
		golog.Info("No Metro2 generation logs found for month", "reportMonth", reportMonth)
		return make(map[string]int), nil
	}

	// 找到最新的记录
	var latestLog bson.M
	var latestTimestamp int64 = -1

	for _, log := range allLogs {
		var logTimestamp int64

		if genDateTime, ok := log["generatedAt"].(primitive.DateTime); ok {
			logTimestamp = int64(genDateTime)
		} else if genTime, ok := log["generatedAt"].(time.Time); ok {
			logTimestamp = genTime.UnixNano() / int64(time.Millisecond)
		} else {
			continue
		}

		if logTimestamp > latestTimestamp {
			latestTimestamp = logTimestamp
			latestLog = log
		}
	}

	if latestLog == nil {
		return make(map[string]int), nil
	}

	// 提取ProcessedLeases
	processedLeasesInterface, ok := latestLog["processedLeases"]
	if !ok {
		return make(map[string]int), nil
	}

	processedLeases, ok := processedLeasesInterface.(bson.A)
	if !ok {
		return make(map[string]int), nil
	}

	// 提取所有leaseID
	leaseIDs := make([]string, 0, len(processedLeases))
	for _, lease := range processedLeases {
		if leaseMap, ok := lease.(bson.M); ok {
			if leaseID, ok := leaseMap["leaseId"].(string); ok {
				leaseIDs = append(leaseIDs, leaseID)
			}
		}
	}

	if len(leaseIDs) == 0 {
		return make(map[string]int), nil
	}

	// 查询这些lease的所有者，按用户统计
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	leaseCursor, err := leaseColl.Find(ctx, bson.M{
		"_id": bson.M{"$in": leaseIDs},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to query leases: %v", err)
	}
	defer leaseCursor.Close(ctx)

	userUsageMap := make(map[string]int)
	for leaseCursor.Next(ctx) {
		var lease struct {
			ID     string `bson:"_id"`
			UserID string `bson:"usrId"`
		}
		if err := leaseCursor.Decode(&lease); err != nil {
			continue
		}
		userUsageMap[lease.UserID]++
	}

	golog.Info("Calculated user usage from Metro2 logs",
		"reportMonth", reportMonth,
		"totalLeases", len(leaseIDs),
		"userCount", len(userUsageMap),
		"generationLogID", latestLog["_id"])

	return userUsageMap, nil
}

// ReportSingleUserUsage 为单个用户向Stripe报告使用量（公共方法）
func (s *MonthlyUsageReportService) ReportSingleUserUsage(ctx context.Context, userID string, overageCount int, year, month int) error {
	return s.reportUsageToStripe(ctx, userID, overageCount, year, month)
}

// reportUsageToStripe 向Stripe报告用户的超量使用量
func (s *MonthlyUsageReportService) reportUsageToStripe(ctx context.Context, userID string, overageCount int, year, month int) error {
	// 1. 获取用户的Stripe客户ID
	stripeCustomerID, err := s.getStripeCustomerID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get Stripe customer ID: %v", err)
	}

	if stripeCustomerID == "" {
		golog.Warn("No Stripe customer ID found for user", "uid", userID)
		return nil // 用户没有Stripe客户ID，跳过
	}

	// 2. 计算时间戳
	// 注意：Stripe要求时间戳不能是未来时间，所以我们总是使用当前时间
	timestamp := time.Now().Unix()

	// 3. 向Stripe报告使用量（注意：这里报告的是超量数量，Stripe会自动按单价计费）
	err = s.createUsageRecord(stripeCustomerID, overageCount, timestamp)
	if err != nil {
		return fmt.Errorf("failed to create usage record in Stripe: %v", err)
	}

	golog.Info("Successfully reported usage to Stripe",
		"userID", userID,
		"stripeCustomerID", stripeCustomerID,
		"overageCount", overageCount,
		"timestamp", timestamp)

	return nil
}

// createUsageRecord 创建Stripe使用量记录（使用新的Billing Meter Events API）
func (s *MonthlyUsageReportService) createUsageRecord(customerID string, quantity int, timestamp int64) error {
	// 从配置文件获取meter事件名称
	meterEventName := goconfig.Config("stripe.extra_report_meter_event_name").(string)
	if meterEventName == "" {
		return fmt.Errorf("extra_report_meter_event_name not configured")
	}

	// 使用新的Billing Meter Events API
	// 构建请求数据
	data := url.Values{}
	data.Set("event_name", meterEventName) // 从配置文件读取事件名称
	data.Set("payload[value]", fmt.Sprintf("%d", quantity))
	data.Set("payload[stripe_customer_id]", customerID)
	data.Set("timestamp", fmt.Sprintf("%d", timestamp))

	// 创建HTTP请求 - 使用新的API端点
	apiURL := "https://api.stripe.com/v1/billing/meter_events"
	req, err := http.NewRequest("POST", apiURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+getStripeSecretKey())
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		var errorResponse map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&errorResponse); err == nil {
			return fmt.Errorf("Stripe API error: %v", errorResponse)
		}
		return fmt.Errorf("Stripe API error: status %d", resp.StatusCode)
	}

	return nil
}

// getExtraReportSubscriptionItemId 获取用户的extra_report订阅项ID
func (s *MonthlyUsageReportService) getExtraReportSubscriptionItemId(ctx context.Context, userID string) (string, error) {
	// 1. 从数据库获取用户的extra_report订阅记录
	userSubColl := gomongo.Coll("rr", "usersub")
	if userSubColl == nil {
		return "", fmt.Errorf("usersub collection not initialized")
	}

	var extraReportSub bson.M
	err := userSubColl.FindOne(ctx, bson.M{
		"uid":    userID,
		"planId": "extra_report_plan", // 特殊标识
		"sts":    "active",
	}).Decode(&extraReportSub)

	if err != nil {
		return "", fmt.Errorf("failed to find extra_report subscription: %v", err)
	}

	stripeSubId, ok := extraReportSub["stripeSubId"].(string)
	if !ok || stripeSubId == "" {
		return "", fmt.Errorf("invalid stripe subscription ID")
	}

	// 2. 从Stripe获取订阅详情
	subscription, err := sub.Get(stripeSubId, nil)
	if err != nil {
		return "", fmt.Errorf("failed to get Stripe subscription: %v", err)
	}

	// 3. 查找extra_report对应的subscription item
	for _, item := range subscription.Items.Data {
		if item.Plan.ID == s.ExtraReportPriceId {
			return item.ID, nil
		}
	}

	return "", fmt.Errorf("extra_report subscription item not found in Stripe subscription")
}

// getStripeCustomerID 获取用户的Stripe客户ID
func (s *MonthlyUsageReportService) getStripeCustomerID(ctx context.Context, userID string) (string, error) {
	// 确保Stripe密钥已设置
	if stripe.Key == "" {
		if key := getStripeSecretKey(); key != "" {
			stripe.Key = key
		} else {
			return "", fmt.Errorf("Stripe secret key not configured")
		}
	}

	// 1. 从数据库获取用户的订阅记录（任何活跃订阅都可以）
	userSubColl := gomongo.Coll("rr", "usersub")
	if userSubColl == nil {
		return "", fmt.Errorf("usersub collection not initialized")
	}

	var userSub bson.M
	err := userSubColl.FindOne(ctx, bson.M{
		"uid": userID,
		"sts": "active",
	}).Decode(&userSub)

	if err != nil {
		return "", fmt.Errorf("failed to find active subscription: %v", err)
	}

	stripeSubId, ok := userSub["stripeSubId"].(string)
	if !ok || stripeSubId == "" {
		return "", fmt.Errorf("invalid stripe subscription ID")
	}

	// 2. 从Stripe获取订阅详情以获取客户ID
	subscription, err := sub.Get(stripeSubId, nil)
	if err != nil {
		return "", fmt.Errorf("failed to get Stripe subscription: %v", err)
	}

	return subscription.Customer.ID, nil
}

// GetStripeCustomerIDForTesting 用于测试的公开方法
func (s *MonthlyUsageReportService) GetStripeCustomerIDForTesting(ctx context.Context, userID string) (string, error) {
	return s.getStripeCustomerID(ctx, userID)
}

// CalculateUserUsage 计算单个用户的使用量（公共方法，用于测试）
func (s *MonthlyUsageReportService) CalculateUserUsage(ctx context.Context, userID string, year, month int) (int, error) {
	// 获取所有用户的使用量
	userUsageMap, err := s.calculateAllUserUsageFromMetro2(ctx, year, month)
	if err != nil {
		return 0, err
	}

	// 返回指定用户的使用量
	usage, exists := userUsageMap[userID]
	if !exists {
		return 0, fmt.Errorf("no usage data found for user %s in %04d-%02d", userID, year, month)
	}

	baseQuota := getBaseQuota()
	if usage > baseQuota {
		return usage - baseQuota, nil // 返回超量部分
	}

	return 0, nil // 没有超量
}

// CalculateAllUserUsageFromMetro2 计算所有用户的使用量（公共方法，用于测试）
func (s *MonthlyUsageReportService) CalculateAllUserUsageFromMetro2(ctx context.Context, year, month int) (map[string]int, error) {
	return s.calculateAllUserUsageFromMetro2(ctx, year, month)
}

// GetBaseQuota 获取基础配额（公共方法）
func (s *MonthlyUsageReportService) GetBaseQuota() int {
	return getBaseQuota()
}
