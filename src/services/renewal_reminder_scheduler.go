package services

import (
	"context"
	"fmt"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/sub"
	"go.mongodb.org/mongo-driver/bson"
)

// RenewalReminderConfig 续订提醒配置
type RenewalReminderConfig struct {
	Enabled                bool
	ReminderDays           int
	ProcessIntervalMinutes int
	TestMode               bool
}

// getRenewalReminderConfig 获取续订提醒配置
func getRenewalReminderConfig() RenewalReminderConfig {
	config := RenewalReminderConfig{
		Enabled:                true,
		ReminderDays:           7,    // 默认提前7天
		ProcessIntervalMinutes: 1440, // 默认每24小时检查一次
		TestMode:               false,
	}

	// 从配置文件读取
	if enabled := goconfig.Config("renewalReminderEmail.enabled"); enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	if reminderDays := goconfig.Config("renewalReminderEmail.reminderDays"); reminderDays != nil {
		if days, ok := reminderDays.(int); ok {
			config.ReminderDays = days
		}
	}

	if processInterval := goconfig.Config("renewalReminderEmail.processIntervalMinutes"); processInterval != nil {
		if interval, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = interval
		}
	}

	if testMode := goconfig.Config("renewalReminderEmail.testMode"); testMode != nil {
		if test, ok := testMode.(bool); ok {
			config.TestMode = test
		}
	}

	return config
}

// StartRenewalReminderScheduler 启动续订提醒调度器
func StartRenewalReminderScheduler() {
	config := getRenewalReminderConfig()

	if !config.Enabled {
		golog.Info("Renewal reminder scheduler is disabled")
		return
	}

	golog.Info("Starting renewal reminder scheduler",
		"reminderDays", config.ReminderDays,
		"testMode", config.TestMode,
		"processIntervalMinutes", config.ProcessIntervalMinutes)

	// 启动处理任务的goroutine
	go startRenewalReminderWorker(config)
}

// startRenewalReminderWorker 启动续订提醒处理工作器
func startRenewalReminderWorker(config RenewalReminderConfig) {
	golog.Info("Renewal reminder processing worker started",
		"intervalMinutes", config.ProcessIntervalMinutes,
		"testMode", config.TestMode,
		"reminderDays", config.ReminderDays)

	// 如果是测试模式，立即执行一次
	if config.TestMode {
		golog.Info("TEST MODE: Sending renewal reminder emails immediately")
		processRenewalReminderTask(config)
	}

	// 然后按间隔执行
	ticker := time.NewTicker(time.Duration(config.ProcessIntervalMinutes) * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		processRenewalReminderTask(config)
	}
}

// processRenewalReminderTask 处理续订提醒任务
func processRenewalReminderTask(config RenewalReminderConfig) {
	ctx := context.Background()

	golog.Info("Starting renewal reminder processing",
		"reminderDays", config.ReminderDays,
		"testMode", config.TestMode)

	// 获取所有活跃的用户订阅
	userSubColl := gomongo.Coll("rr", "usersub")
	cursor, err := userSubColl.Find(ctx, bson.M{
		"sts": "active",
	})
	if err != nil {
		golog.Error("Failed to find active subscriptions", "error", err)
		return
	}
	defer cursor.Close(ctx)

	processedCount := 0
	sentCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var userSub bson.M
		if err := cursor.Decode(&userSub); err != nil {
			golog.Error("Failed to decode user subscription", "error", err)
			errorCount++
			continue
		}

		processedCount++

		// 处理单个订阅
		if err := processSubscriptionReminder(ctx, userSub, config); err != nil {
			golog.Error("Failed to process subscription reminder",
				"error", err,
				"userSubId", userSub["_id"],
				"uid", userSub["uid"])
			errorCount++
		} else {
			sentCount++
		}
	}

	golog.Info("Renewal reminder processing completed",
		"processedCount", processedCount,
		"sentCount", sentCount,
		"errorCount", errorCount)
}

// processSubscriptionReminder 处理单个订阅的续订提醒
func processSubscriptionReminder(ctx context.Context, userSub bson.M, config RenewalReminderConfig) error {
	stripeSubId, ok := userSub["stripeSubId"].(string)
	if !ok || stripeSubId == "" {
		return fmt.Errorf("missing or invalid stripeSubId")
	}

	uid, ok := userSub["uid"].(string)
	if !ok || uid == "" {
		return fmt.Errorf("missing or invalid uid")
	}

	// 从 Stripe 获取订阅信息
	subscription, err := sub.Get(stripeSubId, nil)
	if err != nil {
		return fmt.Errorf("failed to get Stripe subscription: %v", err)
	}

	// 检查订阅状态
	if subscription.Status != stripe.SubscriptionStatusActive {
		golog.Debug("Subscription is not active, skipping",
			"stripeSubId", stripeSubId,
			"status", subscription.Status)
		return nil
	}

	// 计算下次计费日期
	nextBillingDate := time.Unix(subscription.CurrentPeriodEnd, 0)

	// 计算提醒日期（提前N天）
	reminderDate := nextBillingDate.AddDate(0, 0, -config.ReminderDays)
	now := time.Now()

	// 检查是否应该发送提醒
	if config.TestMode {
		golog.Info("TEST MODE: Sending reminder regardless of date",
			"uid", uid,
			"nextBillingDate", nextBillingDate.Format("2006-01-02"),
			"reminderDate", reminderDate.Format("2006-01-02"))
	} else {
		// 检查是否在提醒日期范围内（当天或已过期但未超过1天）
		if now.Before(reminderDate) || now.After(reminderDate.AddDate(0, 0, 1)) {
			golog.Debug("Not in reminder date range, skipping",
				"uid", uid,
				"now", now.Format("2006-01-02"),
				"reminderDate", reminderDate.Format("2006-01-02"),
				"nextBillingDate", nextBillingDate.Format("2006-01-02"))
			return nil
		}
	}

	// 检查是否已经发送过提醒
	if hasReminderBeenSent(ctx, uid, stripeSubId, nextBillingDate) {
		golog.Debug("Reminder already sent for this billing cycle, skipping",
			"uid", uid,
			"stripeSubId", stripeSubId,
			"nextBillingDate", nextBillingDate.Format("2006-01-02"))
		return nil
	}

	// 获取用户信息
	userColl := gomongo.Coll("rr", "users")
	var user bson.M
	if err := userColl.FindOne(ctx, bson.M{"_id": uid}).Decode(&user); err != nil {
		return fmt.Errorf("failed to get user: %v", err)
	}

	email, ok := user["email"].(string)
	if !ok || email == "" {
		return fmt.Errorf("user has no email address")
	}

	firstName := ""
	lastName := ""
	if name, ok := user["usrNm"].(string); ok {
		firstName = name
		// 如果需要分离姓和名，可以在这里处理
		// 目前假设 usrNm 是全名，lastName 留空
	}

	// 获取订阅计划信息
	planName := "Professional"   // 默认值
	subscriptionCost := "$17.99" // 默认值，可以从数据库中获取

	if planId, ok := userSub["planId"].(string); ok && planId != "" {
		subplanColl := gomongo.Coll("rr", "subplan")
		var subplan bson.M
		if err := subplanColl.FindOne(ctx, bson.M{"_id": planId}).Decode(&subplan); err == nil {
			if nm, ok := subplan["nm"].(string); ok && nm != "" {
				planName = nm
			}
		}
	}

	// 发送提醒邮件
	golog.Info("Sending renewal reminder email",
		"uid", uid,
		"email", email,
		"planName", planName,
		"nextBillingDate", nextBillingDate.Format("2006-01-02"))

	if err := utils.SendRenewalReminderEmail(email, firstName, lastName, planName, subscriptionCost, nextBillingDate); err != nil {
		return fmt.Errorf("failed to send renewal reminder email: %v", err)
	}

	// 记录已发送提醒
	if err := recordReminderSent(ctx, uid, stripeSubId, nextBillingDate); err != nil {
		golog.Error("Failed to record reminder sent", "error", err, "uid", uid)
		// 不返回错误，因为邮件已经发送成功
	}

	golog.Info("Renewal reminder email sent successfully",
		"uid", uid,
		"email", email,
		"planName", planName)

	return nil
}

// hasReminderBeenSent 检查是否已经为此计费周期发送过提醒
func hasReminderBeenSent(ctx context.Context, uid, stripeSubId string, nextBillingDate time.Time) bool {
	reminderColl := gomongo.Coll("rr", "renewal_reminders")

	// 查找是否存在相同的提醒记录
	count, err := reminderColl.CountDocuments(ctx, bson.M{
		"uid":             uid,
		"stripeSubId":     stripeSubId,
		"nextBillingDate": nextBillingDate,
	})

	return err == nil && count > 0
}

// recordReminderSent 记录已发送提醒
func recordReminderSent(ctx context.Context, uid, stripeSubId string, nextBillingDate time.Time) error {
	reminderColl := gomongo.Coll("rr", "renewal_reminders")

	reminder := bson.M{
		"_id":             utils.GenerateNanoID(),
		"uid":             uid,
		"stripeSubId":     stripeSubId,
		"nextBillingDate": nextBillingDate,
		"sentAt":          time.Now(),
	}

	_, err := reminderColl.InsertOne(ctx, reminder)
	return err
}
