package services

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"rent_report/config"
	"rent_report/entities"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomail"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Metro2AutoGenerationConfig Metro2自动生成配置
type Metro2AutoGenerationConfig struct {
	Enabled                bool
	SendDay                int
	SendHour               int
	RecipientEmail         string
	ProcessIntervalMinutes int
	TestMode               bool
}

// getMetro2AutoGenerationConfig 获取Metro2自动生成配置
func getMetro2AutoGenerationConfig() Metro2AutoGenerationConfig {
	config := Metro2AutoGenerationConfig{
		Enabled:                true,
		SendDay:                21,
		SendHour:               9,
		RecipientEmail:         "<EMAIL>",
		ProcessIntervalMinutes: 60,
		TestMode:               false,
	}

	// 读取配置
	if enabled := goconfig.Config("metro2AutoGeneration.enabled"); enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	if sendDay := goconfig.Config("metro2AutoGeneration.sendDay"); sendDay != nil {
		if dayInt, ok := sendDay.(int); ok {
			config.SendDay = dayInt
		} else if dayInt64, ok := sendDay.(int64); ok {
			config.SendDay = int(dayInt64)
		}
	}

	if sendHour := goconfig.Config("metro2AutoGeneration.sendHour"); sendHour != nil {
		if hourInt, ok := sendHour.(int); ok {
			config.SendHour = hourInt
		} else if hourInt64, ok := sendHour.(int64); ok {
			config.SendHour = int(hourInt64)
		}
	}

	if recipientEmail := goconfig.Config("metro2AutoGeneration.recipientEmail"); recipientEmail != nil {
		if emailStr, ok := recipientEmail.(string); ok && emailStr != "" {
			config.RecipientEmail = emailStr
		}
	}

	if processInterval := goconfig.Config("metro2AutoGeneration.processIntervalMinutes"); processInterval != nil {
		if intervalInt, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = intervalInt
		} else if intervalInt64, ok := processInterval.(int64); ok {
			config.ProcessIntervalMinutes = int(intervalInt64)
		}
	}

	if testMode := goconfig.Config("metro2AutoGeneration.testMode"); testMode != nil {
		if testBool, ok := testMode.(bool); ok {
			config.TestMode = testBool
		}
	}

	return config
}

// StartMetro2AutoGenerationScheduler 启动Metro2自动生成调度器
func StartMetro2AutoGenerationScheduler() {

	config := getMetro2AutoGenerationConfig()

	if !config.Enabled {
		return
	}
	// 启动处理任务的goroutine
	go startMetro2AutoGenerationWorker(config)
}

// startMetro2AutoGenerationWorker 启动Metro2自动生成处理工作器
func startMetro2AutoGenerationWorker(config Metro2AutoGenerationConfig) {

	// 如果是测试模式，立即执行一次
	if config.TestMode {
		processMetro2AutoGenerationTask(config)
	}

	// 然后按间隔执行
	ticker := time.NewTicker(time.Duration(config.ProcessIntervalMinutes) * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		if !config.TestMode {
			// 生产模式：检查是否是发送日期和时间
			now := time.Now()
			golog.Info("Checking if today is generation day and hour",
				"currentDay", now.Day(),
				"currentHour", now.Hour(),
				"sendDay", config.SendDay,
				"sendHour", config.SendHour)

			if now.Day() == config.SendDay && now.Hour() == config.SendHour {
				// 检查今天是否已经生成过（防重复机制）
				if !hasAutoGeneratedThisMonth() {
					golog.Info("Generating Metro2 files and sending email for today")
					processMetro2AutoGenerationTask(config)
				} else {
					golog.Info("Already generated Metro2 files for this month, skipping")
				}
			} else {
				golog.Info("Not generation day/hour yet, waiting...")
			}
		}
	}
}

// hasAutoGeneratedThisMonth 检查当月是否已经自动生成过Metro2文件
func hasAutoGeneratedThisMonth() bool {
	// 这里可以查询metro2_generation_logs表来检查
	// 为了简化，暂时返回false，实际实现时可以添加数据库查询
	return false
}

// reportUsageAfterMetro2Generation Metro2生成完成后立即上报使用量
func reportUsageAfterMetro2Generation(ctx context.Context, reportMonth time.Time) error {
	golog.Info("Starting usage reporting after Metro2 generation",
		"reportMonth", reportMonth.Format("2006-01"))

	// 创建使用量报告服务
	usageService := NewMonthlyUsageReportService()

	// 获取报告月份的年月
	year := reportMonth.Year()
	month := int(reportMonth.Month())

	// 获取所有用户的使用量（基于刚生成的Metro2记录）
	userUsageMap, err := usageService.calculateAllUserUsageFromMetro2(ctx, year, month)
	if err != nil {
		return fmt.Errorf("failed to calculate user usage from Metro2: %v", err)
	}

	golog.Info("Calculated user usage from Metro2",
		"userCount", len(userUsageMap),
		"reportMonth", reportMonth.Format("2006-01"))

	if len(userUsageMap) == 0 {
		golog.Info("No user usage found, skipping usage reporting")
		return nil
	}

	// 获取基础配额
	baseQuota := usageService.GetBaseQuota()

	successCount := 0
	errorCount := 0

	// 为每个有超量使用的用户报告到Stripe
	for userID, usageCount := range userUsageMap {
		if usageCount > baseQuota {
			overageCount := usageCount - baseQuota

			// 检查是否已经上报过这个月的数据（防重复）
			if hasReportedUsageThisMonth(ctx, userID, year, month) {
				golog.Info("Usage already reported for this month, skipping",
					"userID", userID,
					"reportMonth", reportMonth.Format("2006-01"))
				continue
			}

			err := usageService.reportUsageToStripe(ctx, userID, overageCount, year, month)
			if err != nil {
				golog.Error("Failed to report usage to Stripe",
					"error", err,
					"userID", userID,
					"overageCount", overageCount,
					"reportMonth", reportMonth.Format("2006-01"))
				errorCount++
			} else {
				golog.Info("Successfully reported usage to Stripe",
					"userID", userID,
					"totalUsage", usageCount,
					"overageCount", overageCount,
					"reportMonth", reportMonth.Format("2006-01"))

				// 记录已上报状态
				recordUsageReported(ctx, userID, year, month, overageCount, "auto_after_metro2")
				successCount++
			}
		} else {
			golog.Debug("No overage usage for user",
				"userID", userID,
				"usage", usageCount,
				"baseQuota", baseQuota)
		}
	}

	golog.Info("Usage reporting completed after Metro2 generation",
		"successCount", successCount,
		"errorCount", errorCount,
		"totalUsers", len(userUsageMap),
		"reportMonth", reportMonth.Format("2006-01"))

	return nil
}

// hasReportedUsageThisMonth 检查是否已经上报过这个月的使用量
func hasReportedUsageThisMonth(ctx context.Context, userID string, year, month int) bool {
	coll := gomongo.Coll("rr", "usage_reports")
	if coll == nil {
		golog.Warn("usage_reports collection not initialized")
		return false
	}

	reportMonth := fmt.Sprintf("%04d-%02d", year, month)

	var result bson.M
	err := coll.FindOne(ctx, bson.M{
		"userID":      userID,
		"reportMonth": reportMonth,
	}).Decode(&result)

	return err == nil // 如果找到记录，说明已经上报过
}

// recordUsageReported 记录已上报的使用量
func recordUsageReported(ctx context.Context, userID string, year, month int, overageCount int, reportType string) {
	coll := gomongo.Coll("rr", "usage_reports")
	if coll == nil {
		golog.Warn("usage_reports collection not initialized")
		return
	}

	reportMonth := fmt.Sprintf("%04d-%02d", year, month)

	record := bson.M{
		"userID":       userID,
		"reportMonth":  reportMonth,
		"overageCount": overageCount,
		"reportedAt":   time.Now(),
		"reportType":   reportType, // 标识上报类型
	}

	// 使用upsert确保不重复插入
	_, err := coll.ReplaceOne(ctx, bson.M{
		"userID":      userID,
		"reportMonth": reportMonth,
	}, record, &options.ReplaceOptions{
		Upsert: &[]bool{true}[0],
	})

	if err != nil {
		golog.Error("Failed to record usage report",
			"error", err,
			"userID", userID,
			"reportMonth", reportMonth)
	} else {
		golog.Debug("Usage report recorded",
			"userID", userID,
			"reportMonth", reportMonth,
			"overageCount", overageCount)
	}
}

// processMetro2AutoGenerationTask 处理Metro2自动生成任务
func processMetro2AutoGenerationTask(config Metro2AutoGenerationConfig) {
	ctx := context.Background()

	// 获取上个月作为报告月份（与admin页面逻辑一致）
	now := time.Now()
	reportMonth := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, now.Location())

	// 获取一个实际存在的admin用户ID
	adminUserID, err := getAdminUserID(ctx)
	if err != nil {
		return
	}

	// 生成Metro2文件数据
	jsonData, generationLog, err := entities.GenerateMetro2FileData(ctx, reportMonth, adminUserID)
	if err != nil {
		return
	}

	// 生成Metro2文件
	metro2File, err := utils.GenerateMetro2File(string(jsonData))
	if err != nil {
		return
	}

	// 使用goupload保存JSON文件
	jsonFilePath, err := saveJSONFileWithGoupload(ctx, jsonData, reportMonth, adminUserID)
	if err != nil {
		return
	}

	// 使用goupload保存Metro2文件
	metro2FilePath, err := saveMetro2FileWithGoupload(ctx, metro2File, reportMonth, adminUserID)
	if err != nil {
		return
	}

	// 保存生成日志到数据库
	generationLog.JSONBackupPath = jsonFilePath
	generationLog.FileName = fmt.Sprintf("Metro2-Auto-Generated-%s.txt", reportMonth.Format("2006-01"))
	generationLog.FileSize = int64(len(metro2File))
	if err := generationLog.Create(ctx); err != nil {
		golog.Error("Failed to save Metro2 generation log", "error", err)
		return
	}

	// Metro2生成完成后立即上报使用量
	golog.Info("Metro2 generation completed, starting usage reporting",
		"reportMonth", reportMonth.Format("2006-01"),
		"generationLogID", generationLog.ID)

	err = reportUsageAfterMetro2Generation(ctx, reportMonth)
	if err != nil {
		golog.Error("Failed to report usage after Metro2 generation",
			"error", err,
			"reportMonth", reportMonth.Format("2006-01"))
		// 不返回错误，因为Metro2生成已经成功，使用量上报失败不应该影响主流程
	}

	// 创建Metro2通知任务（如果需要）
	err = createMetro2NotificationTask(ctx, generationLog)
	if err != nil {
		golog.Warn("Failed to create Metro2 notification task", "error", err)
	}

	// 发送邮件，附带两个文件
	err = sendMetro2AutoGenerationEmailWithFiles(config.RecipientEmail, jsonFilePath, metro2FilePath, reportMonth, generationLog)
	if err != nil {
		return
	}
}

// saveJSONFileWithGoupload 使用goupload保存JSON文件
func saveJSONFileWithGoupload(ctx context.Context, jsonData []byte, reportMonth time.Time, userID string) (string, error) {
	// 生成JSON文件名
	now := time.Now()
	fileName := fmt.Sprintf("RM-Metro2-Backup-%s-%s.json", reportMonth.Format("2006-01"), now.Format("20060102-150405"))

	golog.Info("Saving JSON file with goupload", "fileName", fileName, "userID", userID)

	// 创建JSON数据的reader
	jsonReader := bytes.NewReader(jsonData)

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.Metro2Reports, statsColl)
	if err != nil {
		return "", fmt.Errorf("failed to create stats updater for JSON file: %v", err)
	}

	// 使用goupload保存JSON文件
	result, err := goupload.Upload(
		ctx,
		statsUpdater,               // statsUpdater
		uploadConfig.Site,          // site
		uploadConfig.Metro2Reports, // entryName
		userID,                     // uid
		jsonReader,                 // reader
		fileName,                   // originalFilename
		int64(len(jsonData)),       // clientDeclaredSize
	)
	if err != nil {
		return "", fmt.Errorf("failed to upload JSON file: %v", err)
	}

	golog.Info("JSON file saved successfully with goupload", "path", result.Path, "size", result.Size)
	return result.Path, nil
}

// saveMetro2FileWithGoupload 使用goupload保存Metro2文件
func saveMetro2FileWithGoupload(ctx context.Context, metro2Data []byte, reportMonth time.Time, userID string) (string, error) {
	// 生成Metro2文件名
	now := time.Now()
	fileName := fmt.Sprintf("RM-Metro2-%s-%s.txt", reportMonth.Format("2006-01"), now.Format("20060102-150405"))

	golog.Info("Saving Metro2 file with goupload", "fileName", fileName, "userID", userID)

	// 创建Metro2数据的reader
	metro2Reader := bytes.NewReader(metro2Data)

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.Metro2Reports, statsColl)
	if err != nil {
		return "", fmt.Errorf("failed to create stats updater for Metro2 file: %v", err)
	}

	// 使用goupload保存Metro2文件
	result, err := goupload.Upload(
		ctx,
		statsUpdater,               // statsUpdater
		uploadConfig.Site,          // site
		uploadConfig.Metro2Reports, // entryName
		userID,                     // uid
		metro2Reader,               // reader
		fileName,                   // originalFilename
		int64(len(metro2Data)),     // clientDeclaredSize
	)
	if err != nil {
		return "", fmt.Errorf("failed to upload Metro2 file: %v", err)
	}

	golog.Info("Metro2 file saved successfully with goupload", "path", result.Path, "size", result.Size)
	return result.Path, nil
}

// sendMetro2AutoGenerationEmailWithFiles 发送Metro2自动生成邮件，附带文件
func sendMetro2AutoGenerationEmailWithFiles(recipientEmail, jsonFilePath, metro2FilePath string, reportMonth time.Time, generationLog *entities.Metro2GenerationLog) error {
	// 获取Metro2报告的存储路径
	storagePath := config.GetStoragePath("metro2_reports")

	// 构建完整的文件路径
	fullJsonPath := fmt.Sprintf("%s/%s", storagePath, jsonFilePath)
	fullMetro2Path := fmt.Sprintf("%s/%s", storagePath, metro2FilePath)

	// 检查文件是否存在
	if _, err := os.Stat(fullJsonPath); os.IsNotExist(err) {
		return fmt.Errorf("JSON file not found: %s", fullJsonPath)
	}
	if _, err := os.Stat(fullMetro2Path); os.IsNotExist(err) {
		return fmt.Errorf("Metro2 file not found: %s", fullMetro2Path)
	}
	// 生成邮件主题
	subject := fmt.Sprintf("Metro2 Monthly Report - %s", reportMonth.Format("January 2006"))

	// 生成邮件内容
	htmlBody := fmt.Sprintf(`
		<h2>Metro2 Monthly Report - %s</h2>

		<p>Dear Administrator,</p>

		<p>The Metro2 monthly report has been automatically generated and is attached to this email.</p>

		<h3>Report Summary:</h3>
		<ul>
			<li><strong>Report Month:</strong> %s</li>
			<li><strong>Generated At:</strong> %s</li>
			<li><strong>Total Leases Processed:</strong> %d</li>
			<li><strong>Total Tenants:</strong> %d</li>
			<li><strong>Total Payments:</strong> %d</li>
		</ul>

		<h3>Files Included:</h3>
		<ul>
			<li>Metro2 Report File (.txt) - Standard Metro2 format for credit bureau submission</li>
			<li>JSON Backup File (.json) - Detailed data backup for internal records</li>
		</ul>

		<p>Please review the attached files and submit the Metro2 report to the appropriate credit bureaus as needed.</p>

		<p>If you have any questions or concerns about this report, please contact the system administrator.</p>

		<p>Best regards,<br/>
		Report Rentals System<br/>
		Automated Metro2 Generation Service</p>

		<hr>
		<p style="font-size: 12px; color: #666;">
			This is an automated email generated by the Report Rentals system.
			Generated on %s.
		</p>
	`,
		reportMonth.Format("January 2006"),
		reportMonth.Format("2006-01"),
		time.Now().Format("2006-01-02 15:04:05 MST"),
		generationLog.TotalLeases,
		generationLog.TotalTenants,
		generationLog.TotalPayments,
		time.Now().Format("2006-01-02 15:04:05 MST"))

	// 获取邮件发送器
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// 创建附件列表，使用新的结构
	attachments := []gomail.Attachment{
		{
			FilePath:    fullJsonPath,
			DisplayName: fmt.Sprintf("Metro2-Report-%s.json", reportMonth.Format("2006-01")),
		},
		{
			FilePath:    fullMetro2Path,
			DisplayName: fmt.Sprintf("Metro2-Report-%s.txt", reportMonth.Format("2006-01")),
		},
	}

	// 创建邮件消息
	msg := &gomail.EmailMessage{
		To:             []string{recipientEmail},
		Subject:        subject,
		HTML:           htmlBody,
		Engine:         "gmail",
		AttachmentList: attachments,
	}

	// 发送邮件
	err = mailer.SendMail("gmail", msg)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}

	golog.Info("Metro2 auto generation email sent successfully",
		"recipientEmail", recipientEmail,
		"subject", subject,
		"jsonFilePath", jsonFilePath,
		"metro2FilePath", metro2FilePath,
		"reportMonth", reportMonth.Format("2006-01"))

	return nil
}

// getAdminUserID 获取一个实际存在的admin用户ID
func getAdminUserID(ctx context.Context) (string, error) {
	// 方法1：尝试查找第一个用户作为admin用户
	coll := gomongo.Coll("rr", "users")

	// 查找第一个用户
	var user struct {
		ID string `bson:"_id"`
	}

	err := coll.FindOne(ctx, map[string]interface{}{}).Decode(&user)
	if err != nil {
		return "", fmt.Errorf("failed to find any user: %v", err)
	}

	return user.ID, nil
}

// createMetro2NotificationTask 创建Metro2通知任务
func createMetro2NotificationTask(ctx context.Context, generationLog *entities.Metro2GenerationLog) error {
	// 直接调用services包中的标准函数，确保使用相同的配置和逻辑
	return CreateMetro2NotificationTask(ctx, generationLog)
}
