package utils

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"rent_report/config"

	"github.com/real-rm/golog"
)

// InvoicePDFDownloader PDF下载器结构
type InvoicePDFDownloader struct {
	httpClient  *http.Client
	storagePath string
	tmpPath     string
	maxFileSize int64
	maxRetries  int
	retryDelay  time.Duration
}

// NewInvoicePDFDownloader 创建新的PDF下载器
func NewInvoicePDFDownloader() *InvoicePDFDownloader {
	return &InvoicePDFDownloader{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		storagePath: config.GetStoragePath("invoice_pdfs"),
		tmpPath:     config.GetTmpPath("invoice_pdfs"),
		maxFileSize: 10 * 1024 * 1024, // 10MB
		maxRetries:  3,
		retryDelay:  2 * time.Second,
	}
}

// DownloadAndStoreInvoicePDF 下载并存储Invoice PDF
// 返回存储的文件路径，如果失败返回空字符串和错误
func (d *InvoicePDFDownloader) DownloadAndStoreInvoicePDF(invoiceID, pdfURL string) (string, error) {
	if pdfURL == "" {
		return "", fmt.Errorf("PDF URL is empty")
	}

	if invoiceID == "" {
		return "", fmt.Errorf("invoice ID is empty")
	}

	golog.Info("Starting invoice PDF download", "invoiceId", invoiceID, "url", pdfURL)

	// 生成唯一的文件名
	filename := d.generateFilename(invoiceID)
	tmpFilePath := filepath.Join(d.tmpPath, filename)
	finalFilePath := filepath.Join(d.storagePath, filename)

	// 确保目录存在
	if err := d.ensureDirectories(); err != nil {
		return "", fmt.Errorf("failed to ensure directories: %v", err)
	}

	// 下载到临时文件
	if err := d.downloadWithRetry(pdfURL, tmpFilePath); err != nil {
		return "", fmt.Errorf("failed to download PDF: %v", err)
	}

	// 验证文件
	if err := d.validatePDFFile(tmpFilePath); err != nil {
		os.Remove(tmpFilePath) // 清理无效文件
		return "", fmt.Errorf("PDF validation failed: %v", err)
	}

	// 移动到最终存储位置
	if err := d.moveToFinalLocation(tmpFilePath, finalFilePath); err != nil {
		os.Remove(tmpFilePath) // 清理临时文件
		return "", fmt.Errorf("failed to move to final location: %v", err)
	}

	golog.Info("Invoice PDF downloaded and stored successfully",
		"invoiceId", invoiceID,
		"filePath", finalFilePath)

	return finalFilePath, nil
}

// generateFilename 生成唯一的文件名
func (d *InvoicePDFDownloader) generateFilename(invoiceID string) string {
	timestamp := time.Now().Format("20060102_150405")
	// 清理invoiceID中的特殊字符
	cleanInvoiceID := strings.ReplaceAll(invoiceID, "/", "_")
	cleanInvoiceID = strings.ReplaceAll(cleanInvoiceID, "\\", "_")
	return fmt.Sprintf("invoice_%s_%s.pdf", cleanInvoiceID, timestamp)
}

// ensureDirectories 确保必要的目录存在
func (d *InvoicePDFDownloader) ensureDirectories() error {
	dirs := []string{d.tmpPath, d.storagePath}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dir, err)
		}
	}
	return nil
}

// downloadWithRetry 带重试机制的下载
func (d *InvoicePDFDownloader) downloadWithRetry(url, filePath string) error {
	var lastErr error

	for attempt := 1; attempt <= d.maxRetries; attempt++ {
		golog.Debug("Attempting PDF download", "attempt", attempt, "url", url)

		if err := d.downloadFile(url, filePath); err != nil {
			lastErr = err
			golog.Warn("PDF download attempt failed",
				"attempt", attempt,
				"error", err.Error())

			if attempt < d.maxRetries {
				time.Sleep(d.retryDelay)
				continue
			}
		} else {
			golog.Debug("PDF download successful", "attempt", attempt)
			return nil
		}
	}

	return fmt.Errorf("failed after %d attempts, last error: %v", d.maxRetries, lastErr)
}

// downloadFile 下载文件的核心逻辑
func (d *InvoicePDFDownloader) downloadFile(url, filePath string) error {
	// 创建HTTP请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "RentReport-InvoiceDownloader/1.0")

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP error: %d %s", resp.StatusCode, resp.Status)
	}

	// 检查Content-Type
	contentType := resp.Header.Get("Content-Type")
	if !strings.Contains(strings.ToLower(contentType), "pdf") {
		golog.Warn("Unexpected content type", "contentType", contentType)
	}

	// 检查文件大小
	if resp.ContentLength > 0 && resp.ContentLength > d.maxFileSize {
		return fmt.Errorf("file too large: %d bytes (max: %d)", resp.ContentLength, d.maxFileSize)
	}

	// 创建临时文件
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer file.Close()

	// 使用LimitReader防止文件过大
	limitedReader := io.LimitReader(resp.Body, d.maxFileSize)

	// 复制数据
	written, err := io.Copy(file, limitedReader)
	if err != nil {
		return fmt.Errorf("failed to write file: %v", err)
	}

	golog.Debug("File downloaded", "bytes", written, "path", filePath)
	return nil
}

// validatePDFFile 验证PDF文件
func (d *InvoicePDFDownloader) validatePDFFile(filePath string) error {
	// 检查文件是否存在
	info, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("file does not exist: %v", err)
	}

	// 检查文件大小
	if info.Size() == 0 {
		return fmt.Errorf("file is empty")
	}

	if info.Size() > d.maxFileSize {
		return fmt.Errorf("file too large: %d bytes", info.Size())
	}

	// 简单的PDF文件头验证
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file for validation: %v", err)
	}
	defer file.Close()

	// 读取前4个字节检查PDF魔数
	header := make([]byte, 4)
	if _, err := file.Read(header); err != nil {
		return fmt.Errorf("failed to read file header: %v", err)
	}

	if string(header) != "%PDF" {
		return fmt.Errorf("invalid PDF file: missing PDF header")
	}

	golog.Debug("PDF file validation passed", "path", filePath, "size", info.Size())
	return nil
}

// moveToFinalLocation 移动文件到最终位置
func (d *InvoicePDFDownloader) moveToFinalLocation(tmpPath, finalPath string) error {
	// 如果目标文件已存在，先删除
	if _, err := os.Stat(finalPath); err == nil {
		golog.Warn("Final file already exists, removing", "path", finalPath)
		if err := os.Remove(finalPath); err != nil {
			return fmt.Errorf("failed to remove existing file: %v", err)
		}
	}

	// 移动文件
	if err := os.Rename(tmpPath, finalPath); err != nil {
		return fmt.Errorf("failed to move file: %v", err)
	}

	golog.Debug("File moved to final location", "from", tmpPath, "to", finalPath)
	return nil
}

// DownloadAndStoreInvoicePDF 全局便捷函数
func DownloadAndStoreInvoicePDF(invoiceID, pdfURL string) (string, error) {
	downloader := NewInvoicePDFDownloader()
	return downloader.DownloadAndStoreInvoicePDF(invoiceID, pdfURL)
}

// CleanupOldInvoicePDFs 清理旧的Invoice PDF文件
func CleanupOldInvoicePDFs(daysOld int) error {
	if daysOld <= 0 {
		return fmt.Errorf("daysOld must be positive")
	}

	storagePath := config.GetStoragePath("invoice_pdfs")
	cutoffTime := time.Now().AddDate(0, 0, -daysOld)

	golog.Info("Starting cleanup of old invoice PDFs",
		"storagePath", storagePath,
		"cutoffTime", cutoffTime.Format("2006-01-02"))

	entries, err := os.ReadDir(storagePath)
	if err != nil {
		if os.IsNotExist(err) {
			golog.Debug("Invoice PDFs directory does not exist, skipping cleanup")
			return nil
		}
		return fmt.Errorf("failed to read directory: %v", err)
	}

	deletedCount := 0
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		// 只处理PDF文件
		if !strings.HasSuffix(strings.ToLower(entry.Name()), ".pdf") {
			continue
		}

		filePath := filepath.Join(storagePath, entry.Name())
		info, err := entry.Info()
		if err != nil {
			golog.Warn("Failed to get file info", "file", entry.Name(), "error", err)
			continue
		}

		if info.ModTime().Before(cutoffTime) {
			if err := os.Remove(filePath); err != nil {
				golog.Error("Failed to delete old PDF", "file", filePath, "error", err)
			} else {
				golog.Debug("Deleted old PDF", "file", filePath, "age", time.Since(info.ModTime()))
				deletedCount++
			}
		}
	}

	golog.Info("Cleanup completed", "deletedFiles", deletedCount)
	return nil
}

// GetInvoicePDFPath 根据invoiceID获取PDF文件路径（如果存在）
func GetInvoicePDFPath(invoiceID string) (string, bool) {
	if invoiceID == "" {
		return "", false
	}

	storagePath := config.GetStoragePath("invoice_pdfs")

	entries, err := os.ReadDir(storagePath)
	if err != nil {
		golog.Debug("Failed to read invoice PDFs directory", "error", err)
		return "", false
	}

	// 查找匹配的文件
	prefix := fmt.Sprintf("invoice_%s_", strings.ReplaceAll(invoiceID, "/", "_"))
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		if strings.HasPrefix(entry.Name(), prefix) && strings.HasSuffix(strings.ToLower(entry.Name()), ".pdf") {
			filePath := filepath.Join(storagePath, entry.Name())
			// 验证文件是否存在且可读
			if _, err := os.Stat(filePath); err == nil {
				return filePath, true
			}
		}
	}

	return "", false
}
