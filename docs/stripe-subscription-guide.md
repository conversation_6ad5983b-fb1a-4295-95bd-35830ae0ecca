# Stripe订阅系统配置和构建指南

## 概述

本系统采用双订阅架构设计，支持月费和年费两种订阅模式：
- **月费用户**：单一订阅包含所有服务
- **年费用户**：主订阅（年费基础服务） + 辅助订阅（月费extra_report服务）

这种设计允许年费用户享受基础服务的年费优惠，同时对extra_report功能按月计费和使用量计费。

## 1. Stripe端配置（必须先完成）

### 1.1 Products创建

在Stripe Dashboard中创建以下4个产品：

#### 基础订阅产品
1. **normal_monthly_plan**
   - Name: "Professional Monthly Plan"
   - Description: "Monthly subscription with all basic features"
   - Type: Service

2. **normal_annual_plan**
   - Name: "Professional Annual Plan" 
   - Description: "Annual subscription with all basic features"
   - Type: Service

#### Extra Report产品
3. **extra_report_plan**
   - Name: "Extra Report Monthly"
   - Description: "Monthly extra report service for monthly users"
   - Type: Service

4. **extra_report_plan_monthly**
   - Name: "Extra Report for Annual Users"
   - Description: "Monthly extra report service for annual users"
   - Type: Service

### 1.2 Prices创建

为每个产品创建对应的价格：

#### 基础订阅价格
- **normal_monthly_plan**: 
  - Recurring: Monthly
  - Amount: $29.99/month
  
- **normal_annual_plan**:
  - Recurring: Yearly  
  - Amount: $299.99/year

#### Extra Report价格（使用量计费）
- **extra_report_plan**:
  - Type: Usage-based
  - Billing scheme: Per unit
  - Usage type: Metered
  - Recurring: Monthly
  
- **extra_report_plan_monthly**:
  - Type: Usage-based
  - Billing scheme: Per unit  
  - Usage type: Metered
  - Recurring: Monthly

### 1.3 Meters配置

创建使用量计量器：

1. **Extra Report Meter**
   - Event name: `extra_report_usage`
   - Aggregation: `last`
   - Description: "Count of extra reports generated"

2. **关联Meter到Price**
   - 将创建的meter关联到extra_report相关的价格
   - 设置单价（如$5 per report over base quota）

### 1.4 记录所有ID

完成创建后，记录以下ID（后续配置需要）：
```
Products:
- prod_normal_monthly: prod_xxxxxxxxx
- prod_normal_annual: prod_yyyyyyyyy  
- prod_extra_report: prod_zzzzzzzzz
- prod_extra_report_monthly: prod_aaaaaaaaa

Prices:
- price_normal_monthly: price_xxxxxxxxx
- price_normal_annual: price_yyyyyyyyy
- price_extra_report: price_zzzzzzzzz  
- price_extra_report_monthly: price_aaaaaaaaa

Meters:
- meter_extra_report: mtr_xxxxxxxxx
```

## 2. 系统端配置

### 2.1 local.ini配置文件

将获取的ID配置到`configs/local.ini`：

```ini
[stripe]
secret_key = sk_test_your_secret_key
publishable_key = pk_test_your_publishable_key
webhook_secret = whsec_your_webhook_secret

[products]
normal_monthly_plan = prod_xxxxxxxxx
normal_annual_plan = prod_yyyyyyyyy
extra_report_plan = prod_zzzzzzzzz
extra_report_plan_monthly = prod_aaaaaaaaa

[prices]  
normal_monthly_price = price_xxxxxxxxx
normal_annual_price = price_yyyyyyyyy
extra_report_price = price_zzzzzzzzz
extra_report_monthly_price = price_aaaaaaaaa

[meters]
extra_report_meter = mtr_xxxxxxxxx

[rentReportUsage]
baseQuota = 3
```

### 2.2 数据库subplan表配置

在MongoDB的`subplan`集合中创建对应记录：

```javascript
// 月费基础计划
db.subplan.insertOne({
  _id: "normal_monthly_plan",
  nm: "Professional Monthly",
  prdId: "prod_xxxxxxxxx",
  priceId: "price_xxxxxxxxx", 
  intrvl: "month",
  price: 29.99,
  currency: "usd",
  features: ["basic_features", "rent_reporting"],
  active: true
});

// 年费基础计划  
db.subplan.insertOne({
  _id: "normal_annual_plan",
  nm: "Professional Annual",
  prdId: "prod_yyyyyyyyy",
  priceId: "price_yyyyyyyyy",
  intrvl: "year", 
  price: 299.99,
  currency: "usd",
  features: ["basic_features", "rent_reporting"],
  active: true
});

```

## 3. 配置验证

### 3.1 验证配置正确性

```bash
# 检查配置文件
go run tests/test_config_debug.go

# 验证数据库连接和subplan记录
go run tests/simple_db_check.go
```

### 3.2 测试订阅创建

```bash
# 测试checkout session创建
go run tests/test_checkout_session.go
```

## 4. 部署流程总结

### 新环境部署步骤：

1. **Stripe Dashboard配置**
   - [ ] 创建4个Products
   - [ ] 为每个Product创建Price
   - [ ] 创建Extra Report Meter
   - [ ] 记录所有ID

2. **系统配置**  
   - [ ] 更新local.ini配置文件
   - [ ] 创建subplan数据库记录
   - [ ] 配置webhook endpoint

3. **验证测试**
   - [ ] 运行配置验证脚本
   - [ ] 创建测试订阅
   - [ ] 测试webhook接收
   - [ ] 测试订阅取消流程

### 配置依赖关系：
```
Stripe Products → Stripe Prices → Stripe Meters
                     ↓
              local.ini配置文件  
                     ↓
              数据库subplan记录
                     ↓
               系统正常运行
```

## 5. 常见配置问题

### 5.1 Product/Price ID不匹配
- **症状**：订阅创建失败，日志显示"No such price"
- **解决**：检查local.ini中的price ID是否正确

### 5.2 Meter配置错误
- **症状**：使用量上报失败
- **解决**：确认meter ID正确，且已关联到对应价格

### 5.3 数据库记录缺失
- **症状**：系统找不到订阅计划
- **解决**：检查subplan集合中是否有对应记录

## 6. 双订阅模式详解

### 6.1 年费用户的双订阅机制

年费用户会创建两个独立的Stripe订阅：

1. **主订阅（Primary Subscription）**
   - Product: `normal_annual_plan`
   - 计费周期：年费
   - 包含：基础功能、租金报告等
   - billingType: `"annual"`

2. **辅助订阅（Secondary Subscription）**
   - Product: `extra_report_plan_monthly`
   - 计费周期：月费
   - 包含：extra_report功能的使用量计费
   - billingType: `"monthly_for_annual"`
   - primarySubId: 指向主订阅的ID

### 6.2 月费用户的单订阅机制

月费用户只有一个Stripe订阅：
- Product: `normal_monthly_plan` + `extra_report_plan`
- 所有功能打包在一个订阅中
- billingType: `"monthly"`

### 6.3 数据库记录结构

```javascript
// 年费用户的主订阅记录
{
  _id: "M4An9PcL9gN",
  uid: "6RDHxuNP6pT",
  cusId: "cus_Sr5CQwawJLk6V0",
  stripeSubId: "sub_1RvN1sRdRW2qyPyrwKXmbU0V",
  planId: "normal_annual_plan",
  billingType: "annual",
  sts: "active"
}

// 年费用户的辅助订阅记录
{
  _id: "hxyINmFjKOI",
  uid: "6RDHxuNP6pT",
  cusId: "cus_Sr5CQwawJLk6V0",
  stripeSubId: "sub_1RvN1vRdRW2qyPyrpbgc1Gor",
  planId: "extra_report_plan_monthly",
  billingType: "monthly_for_annual",
  primarySubId: "sub_1RvN1sRdRW2qyPyrwKXmbU0V", // 关联主订阅
  sts: "active"
}
```

## 7. Checkout和Webhook流程

### 7.1 年费订阅创建流程

1. **用户选择年费计划**
2. **创建Checkout Session**
   ```go
   // 只包含年费基础服务，不包含extra_report
   lineItems := []*stripe.CheckoutSessionLineItemParams{
       {
           Price:    stripe.String(annualPriceId),
           Quantity: stripe.Int64(1),
       },
   }

   // 设置metadata标识为年费订阅
   metadata := map[string]string{
       "billingType": "annual",
       "prdId": annualProductId,
       "uid": userID,
   }
   ```

3. **用户完成支付**
4. **Webhook接收checkout.session.completed事件**
5. **创建主订阅记录**
6. **异步创建辅助extra_report订阅**
   ```go
   if billingType == "annual" {
       go createSecondaryExtraReportSubscription(uid, cusId, primarySubId)
   }
   ```

### 7.2 月费订阅创建流程

1. **用户选择月费计划**
2. **创建Checkout Session**
   ```go
   // 包含基础服务和extra_report
   lineItems := []*stripe.CheckoutSessionLineItemParams{
       {
           Price:    stripe.String(monthlyPriceId),
           Quantity: stripe.Int64(1),
       },
       {
           Price:    stripe.String(extraReportPriceId),
           Quantity: stripe.Int64(1),
       },
   }
   ```

3. **创建单一订阅记录**

### 7.3 关键Webhook事件处理

```go
switch event.Type {
case "checkout.session.completed":
    // 处理订阅创建

case "customer.subscription.deleted":
    // 处理订阅取消
    handleDualSubscriptionCancellation(uid, cancelledSubId)

case "invoice.paid":
    // 处理续费，上报使用量
    processUsageReportingForRenewal(uid)
}
```

## 8. 订阅取消机制

### 8.1 取消逻辑的复杂性

订阅取消需要根据订阅类型采用不同策略：

#### 月费模式取消
- 取消单一订阅即可
- 更新所有相关的usersub记录状态

#### 年费模式取消
- 如果取消主订阅，需要连带取消辅助订阅
- 如果取消辅助订阅，不影响主订阅

### 8.2 handleDualSubscriptionCancellation函数

```go
func handleDualSubscriptionCancellation(ctx context.Context, userID, cancelledSubID string) error {
    // 1. 查找所有使用该stripeSubId的订阅记录
    allSubs := findSubscriptionsByStripeId(cancelledSubID)

    // 2. 判断订阅模式
    isMonthlyMode := false
    isAnnualMode := false

    for _, sub := range allSubs {
        planId := sub["planId"].(string)
        billingType := sub["billingType"].(string)

        // 年费模式：有billingType为monthly_for_annual的订阅，或者有primarySubId字段
        if billingType == "monthly_for_annual" || sub["primarySubId"] != nil {
            isAnnualMode = true
        }

        // 月费模式：planId为extra_report_plan，或者billingType为monthly且不是年费用户的extra_report
        if planId == "extra_report_plan" || (billingType == "monthly" && planId != "extra_report_plan_monthly") {
            isMonthlyMode = true
        }

        // 年费主订阅：billingType为annual
        if billingType == "annual" {
            isAnnualMode = true
        }
    }

    // 3. 根据模式执行不同的取消策略
    if isMonthlyMode && !isAnnualMode {
        // 月费模式：取消所有使用该stripeSubId的订阅
        updateAllSubscriptionsToInactive(cancelledSubID)
    } else {
        // 年费模式：需要处理关联订阅
        handleAnnualSubscriptionCancellation(userID, cancelledSubID)
    }
}
```

### 8.3 常见取消问题和修复

#### 问题1：年费主订阅取消后，extra_report订阅未取消
- **原因**：订阅类型判断逻辑错误
- **解决**：检查billingType字段判断逻辑

#### 问题2：月费订阅取消不完整
- **原因**：多个服务共享同一个stripeSubId，但只更新了部分记录
- **解决**：确保updateMany操作覆盖所有相关记录

## 9. 使用量计费详解

### 9.1 Extra Report使用量模式

Extra Report功能采用"基础配额 + 超量计费"模式：
- **基础配额**：每月3个免费报告（可配置）
- **超量计费**：超出部分按每个报告收费

### 9.2 使用量计算逻辑

```go
func calculateUsageFromMetro2Logs(userID, reportMonth string) (int, error) {
    // 1. 查找该用户该月的Metro2生成日志
    logs := findMetro2GenerationLogs(userID, reportMonth)

    // 2. 使用最新的生成日志（避免重复计算）
    latestLog := getLatestLog(logs)

    // 3. 返回该次生成涉及的租约数量
    return latestLog.TotalLeases, nil
}

func calculateOverage(usageCount, baseQuota int) int {
    if usageCount <= baseQuota {
        return 0
    }
    return usageCount - baseQuota
}
```

### 9.3 续费时的使用量上报

每次续费时（invoice.paid事件），系统会：

1. **检查用户是否有extra_report订阅**
2. **计算上个月的使用量**
3. **上报到Stripe Usage API**

```go
func processUsageReportingForRenewal(ctx context.Context, userID string) error {
    // 查找extra_report订阅
    extraReportSub := findExtraReportSubscription(userID)
    if extraReportSub == nil {
        return nil // 用户没有extra_report订阅
    }

    // 计算使用量
    year, month := getPreviousMonth()
    usageCount := calculateUsageFromMetro2Logs(userID, fmt.Sprintf("%d-%02d", year, month))
    overageCount := calculateOverage(usageCount, getBaseQuota())

    // 上报到Stripe
    if overageCount > 0 {
        reportUsageToStripe(extraReportSub.StripeSubId, overageCount)
    }

    return nil
}
```

### 9.4 Stripe Usage API调用

```go
func reportUsageToStripe(subscriptionID string, quantity int) error {
    timestamp := time.Now().Unix()

    params := &stripe.UsageRecordParams{
        Quantity:  stripe.Int64(int64(quantity)),
        Timestamp: stripe.Int64(timestamp),
        Action:    stripe.String("set"), // 设置总量，不是增量
    }

    _, err := usagerecord.New(subscriptionID, params)
    return err
}
```

## 10. 故障排查指南

### 10.1 配置问题诊断

#### 问题：订阅创建失败，提示"No such price"
```bash
# 检查步骤
1. 验证local.ini中的price ID是否正确
2. 检查Stripe Dashboard中价格是否存在且激活
3. 确认环境（test/live）是否匹配

# 调试命令
go run tests/test_config_debug.go
```

#### 问题：Meter配置错误，使用量上报失败
```bash
# 检查步骤
1. 确认meter ID在local.ini中配置正确
2. 检查meter是否已关联到对应价格
3. 验证meter的aggregation设置

# 测试使用量上报
go run tests/test_usage_reporting_timestamp.go
```

#### 问题：数据库subplan记录不一致
```bash
# 检查数据库记录
mongosh rr --eval "db.subplan.find().pretty()"

# 验证记录完整性
go run tests/simple_db_check.go
```

### 10.2 运行时问题诊断

#### 问题：年费用户的extra_report订阅未创建
```bash
# 检查日志关键词
grep "Creating secondary monthly extra_report subscription" logs/info.log
grep "Successfully created Stripe subscription" logs/info.log

# 检查数据库记录
mongosh rr --eval "
db.usersub.find({
  'uid': 'USER_ID',
  'billingType': 'monthly_for_annual'
}).pretty()
"
```

#### 问题：订阅取消不完整
```bash
# 检查取消日志
grep "Handling dual subscription cancellation" logs/info.log
grep "Subscription mode detected" logs/info.log

# 验证订阅状态
mongosh rr --eval "
db.usersub.find({
  'uid': 'USER_ID',
  'sts': 'active'
}).pretty()
"
```

#### 问题：使用量计算错误
```bash
# 检查Metro2生成日志
mongosh rr --eval "
db.metro2_generation_logs.find({
  'uid': 'USER_ID'
}).sort({'generatedAt': -1}).limit(5).pretty()
"

# 手动测试使用量计算
go run tests/test_metro2_usage_calculation.go
```

### 10.3 常见错误代码和解决方案

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|---------|
| `No such price: price_xxx` | Price ID配置错误 | 检查local.ini配置 |
| `No such customer: cus_xxx` | Customer ID不存在 | 检查用户的stripeCusIds字段 |
| `Subscription not found` | 订阅已被删除或ID错误 | 检查数据库中的stripeSubId |
| `Invalid usage record` | 使用量上报参数错误 | 检查quantity和timestamp |

### 10.4 监控和告警建议

#### 关键指标监控
- 订阅创建成功率
- Webhook处理成功率
- 使用量上报成功率
- 订阅取消完整性

#### 日志告警规则
```bash
# 订阅创建失败
grep -c "Failed to create.*subscription" logs/info.log

# Webhook处理错误
grep -c "Failed to handle.*webhook" logs/info.log

# 使用量上报失败
grep -c "Failed to report usage" logs/info.log
```

## 11. 最佳实践和注意事项

### 11.1 配置管理最佳实践

1. **环境隔离**
   - 测试环境使用test模式的Stripe密钥
   - 生产环境使用live模式的Stripe密钥
   - 不同环境使用不同的Product/Price ID

2. **版本控制**
   - 将配置文件模板纳入版本控制
   - 敏感信息（API密钥）使用环境变量
   - 记录每次配置变更的原因和影响

3. **备份和恢复**
   - 定期备份Stripe配置（Products/Prices/Meters）
   - 备份数据库subplan记录
   - 制定配置恢复流程

### 11.2 开发和测试建议

1. **本地开发**
   - 使用Stripe CLI进行webhook测试
   - 创建专门的测试用户和订阅
   - 定期清理测试数据

2. **集成测试**
   - 测试完整的订阅生命周期
   - 验证双订阅模式的各种场景
   - 测试异常情况的处理

### 11.3 生产环境注意事项

1. **性能优化**
   - 异步处理耗时操作（如创建辅助订阅）
   - 合理设置HTTP超时时间
   - 监控API调用频率和响应时间

2. **错误处理**
   - 实现重试机制
   - 记录详细的错误日志
   - 设置适当的告警阈值

3. **安全考虑**
   - 验证Webhook签名
   - 限制API访问权限
   - 定期轮换API密钥

---

## 总结

本指南涵盖了Stripe订阅系统的完整配置和运行机制。关键要点：

1. **配置顺序很重要**：Stripe Products → Prices → Meters → local.ini → 数据库记录
2. **双订阅模式复杂但灵活**：年费用户享受基础服务优惠，extra_report按需计费
3. **故障排查需要系统性方法**：从配置到运行时，逐层检查
4. **监控和维护不可忽视**：建立完善的监控体系，及时发现和解决问题

遵循本指南，可以确保订阅系统的稳定运行和持续优化。
