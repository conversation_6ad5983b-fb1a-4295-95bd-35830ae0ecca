[2025-08-08 14:38:33] === Renewal Reminder Scheduler Cron Job Started ===
[2025-08-08 14:38:33] Script directory: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report/scripts
[2025-08-08 14:38:33] Project root: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report
[2025-08-08 14:38:33] CLI binary: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report/build/bin/cli_renewal_reminder
[2025-08-08 14:38:34] Log file: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report/logs/renewal_reminder_scheduler.log
[2025-08-08 14:38:34] Process PID: 88682
[2025-08-08 14:38:34] Another instance is already running (PID: 88682). Exiting.
[2025-08-08 14:39:00] === Renewal Reminder Scheduler Cron Job Started ===
[2025-08-08 14:39:00] Script directory: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report/scripts
[2025-08-08 14:39:00] Project root: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report
[2025-08-08 14:39:00] CLI binary: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report/build/bin/cli_renewal_reminder
[2025-08-08 14:39:00] Log file: /Users/<USER>/OrbStack/rocky/home/<USER>/test/rent_report/logs/renewal_reminder_scheduler.log
[2025-08-08 14:39:00] Process PID: 88751
[2025-08-08 14:39:00] Environment validation passed
[2025-08-08 14:39:00] Environment variables set:
[2025-08-08 14:39:00]   RMBASE_FILE_CFG=configs/local.ini
[2025-08-08 14:39:00] Executing renewal reminder CLI program...
2025/08/08 14:39:00 Requesting GET api.stripe.com/v1/subscriptions/sub_1RttTTRdRW2qyPyrg7OEAtVH

2025/08/08 14:39:00 Request completed in 252.873375ms (retry: 0)
[2025-08-08 14:39:00] SUCCESS: Renewal reminder completed successfully in 0 seconds
[2025-08-08 14:39:00] === Renewal Reminder Scheduler Cron Job Completed ===
