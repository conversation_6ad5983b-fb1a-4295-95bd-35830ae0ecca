2025-08-06T11:41:53.446-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T11:58:59.031-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T12:15:55.702-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T12:54:17.353-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-06T12:57:30.4-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:05:16.354-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:06:02.041-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:06:02.301-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:06:07.545-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:07:01.422-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:08:01.547-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:09:01.658-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:09:01.658-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:10:01.765-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:11:01.876-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:12:02.004-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:12:02.005-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:13:51.528-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T16:19:03.35-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T16:19:50.418-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T16:29:01.625-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T16:56:51.176-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-06T17:28:48.916-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T17:30:19.163-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T17:31:21.286-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T17:32:32.243-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T17:37:55.838-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T17:37:59.204-04:00 level=ERROR msg=Server error, method="GET", path="/v1/user", status=500, duration="8.622865ms", clientIP="::1", responseSize=26, userID="lf3DC2D5XnT", file="security.go:176"
2025-08-06T17:37:59.208-04:00 level=ERROR msg=Server error, method="GET", path="/v1/user", status=500, duration="1.216302ms", clientIP="::1", responseSize=26, userID="lf3DC2D5XnT", file="security.go:176"
2025-08-07T12:45:04.969-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T12:54:09.325-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T13:01:01.617-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T13:04:11.057-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-07T13:06:22.972-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T13:33:11.894-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T14:49:42.951-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T14:58:50.979-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-07T15:03:08.045-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T15:15:58.953-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T15:19:32.939-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T15:31:58.282-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T16:25:08.245-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T16:50:42.148-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T17:29:00.909-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T17:33:17.388-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-07T17:35:27.808-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-07T17:38:19.944-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T11:44:59.862-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T12:07:57.864-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T12:20:50.151-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T12:40:57.227-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T12:48:22.445-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T13:09:55.673-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T13:30:35.715-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-08T15:48:23.11-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T11:25:47.829-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T11:49:54.075-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T11:54:55.456-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T11:57:38.877-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T11:58:29.066-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T11:59:10.876-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T11:59:18.079-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:00:31.687-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:00:40.673-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:01:02.906-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:01:10.087-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T12:01:22.705-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:02:05.735-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T12:06:58.843-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T12:08:01.915-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T12:08:09.595-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:09:10.427-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:10:42.91-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:11:20.993-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:24:17.819-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:24:27.853-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:47:04.174-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:49:15.203-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:49:26.176-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T12:54:20.59-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T13:32:59.916-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T13:46:45.705-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T13:48:32.669-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T13:48:59.431-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T14:09:18.903-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:18.943-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:18.978-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:18.997-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:19.012-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:19.029-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:19.041-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:19.051-04:00 level=ERROR msg=Ignored Email:, file="mail.go:328", <EMAIL>="value"
2025-08-11T14:09:19.061-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-11T14:09:19.097-04:00 level=ERROR msg=Failed to send rent reporting paused notification email, email="<EMAIL>", error={}, file="email.go:619"
2025-08-11T14:09:19.134-04:00 level=ERROR msg=Failed to send rent reporting paused notification email, error={}, file="email.go:619", email="<EMAIL>"
2025-08-11T14:09:19.289-04:00 level=ERROR msg=Failed to send rent reporting paused notification email, email="<EMAIL>", error={}, file="email.go:619"
2025-08-11T14:09:19.301-04:00 level=ERROR msg=Failed to send rent reporting paused notification email, email="<EMAIL>", error={}, file="email.go:619"
2025-08-11T14:14:41.686-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T14:25:16.049-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T14:26:08.571-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T14:30:37.375-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T14:42:41.058-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T14:44:30.867-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T14:45:12.934-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T14:46:47.146-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T14:59:15.827-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T14:59:51.289-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T15:00:01.853-04:00 level=ERROR msg=Server error, userID="6RDHxuNP6pT", file="security.go:176", method="POST", path="/v1/checkout/session", status=500, duration="350.137575ms", clientIP="::1", responseSize=341
2025-08-11T15:00:19.256-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T15:00:22.583-04:00 level=ERROR msg=Server error, path="/v1/checkout/session", status=500, duration="340.1492ms", clientIP="::1", responseSize=341, userID="6RDHxuNP6pT", file="security.go:176", method="POST"
2025-08-11T15:01:44.861-04:00 level=ERROR msg=Server error, status=500, duration="324.460931ms", clientIP="::1", responseSize=341, userID="6RDHxuNP6pT", file="security.go:176", method="POST", path="/v1/checkout/session"
2025-08-11T15:06:34.476-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T15:06:34.501-04:00 level=ERROR msg=Failed to start server, error={
  "Op": "listen",
  "Net": "tcp",
  "Source": null,
  "Addr": {
    "IP": "",
    "Port": 8089,
    "Zone": ""
  },
  "Err": {
    "Syscall": "bind",
    "Err": 48
  }
}, file="main.go:131"
2025-08-11T15:07:18.939-04:00 level=ERROR msg=Server error, status=500, duration="275.623024ms", clientIP="::1", responseSize=341, userID="6RDHxuNP6pT", file="security.go:176", method="POST", path="/v1/checkout/session"
2025-08-11T15:10:35.859-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T15:36:46.317-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T15:47:15.328-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T15:47:43.685-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T15:55:34.252-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T15:58:18.586-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T16:03:09.389-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T16:04:54.705-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T16:05:16.712-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T16:05:47.338-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-11T16:06:48.925-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T16:13:06.612-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-11T16:38:06.19-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T10:31:18.948-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T10:34:07.403-04:00 level=ERROR msg=Failed to generate Metro2 file, file="metro2_report.go:75", error={}, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-12T10:34:07.403-04:00 level=ERROR msg=Server error, path="/v1/metro2/generate", status=500, duration="27.025021ms", clientIP="::1", responseSize=98, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", file="security.go:176", method="POST"
2025-08-12T10:34:14.944-04:00 level=ERROR msg=Failed to generate Metro2 file, error={}, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", file="metro2_report.go:75"
2025-08-12T10:34:14.944-04:00 level=ERROR msg=Server error, responseSize=98, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", file="security.go:176", method="POST", path="/v1/metro2/generate", status=500, duration="12.354255ms", clientIP="::1"
2025-08-12T10:46:30.968-04:00 level=ERROR msg=Failed to remove test file, file="dir_key_store.go:537", l1=100, path="../uploads/metro2_reports/100", error={
  "Op": "remove",
  "Path": "../uploads/metro2_reports/100/.test_write",
  "Err": 2
}
2025-08-12T10:46:30.97-04:00 level=ERROR msg=Failed to save current state to file, file="dir_key_store.go:266", error={}
2025-08-12T10:53:40.395-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T10:53:40.435-04:00 level=ERROR msg=Ignored Email:, file="mail.go:328", <EMAIL>="value"
2025-08-12T10:53:40.458-04:00 level=ERROR msg=Ignored Email:, file="mail.go:328", <EMAIL>="value"
2025-08-12T10:53:40.473-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T10:53:40.491-04:00 level=ERROR msg=Ignored Email:, file="mail.go:328", <EMAIL>="value"
2025-08-12T10:53:40.502-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T10:53:40.511-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T10:53:40.52-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T10:53:40.533-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T10:53:40.855-04:00 level=ERROR msg=Failed to send rent reporting paused notification email, error={}, file="email.go:619", email="<EMAIL>"
2025-08-12T10:53:40.915-04:00 level=ERROR msg=Failed to send rent reporting paused notification email, file="email.go:619", email="<EMAIL>", error={}
2025-08-12T10:53:41.128-04:00 level=ERROR msg=Failed to send rent reporting paused notification email, error={}, file="email.go:619", email="<EMAIL>"
2025-08-12T11:04:19.605-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T11:04:22.498-04:00 level=ERROR msg=Failed to generate Metro2 file, error={}, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", file="metro2_report.go:75"
2025-08-12T11:04:22.498-04:00 level=ERROR msg=Server error, clientIP="::1", responseSize=98, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", file="security.go:176", method="POST", path="/v1/metro2/generate", status=500, duration="49.238893ms"
2025-08-12T11:12:18.74-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T11:22:33.934-04:00 level=ERROR msg=Failed to remove test file, path="../uploads/metro2_reports/100", error={
  "Op": "remove",
  "Path": "../uploads/metro2_reports/100/.test_write",
  "Err": 2
}, file="dir_key_store.go:537", l1=100
2025-08-12T11:22:33.935-04:00 level=ERROR msg=Failed to save current state to file, error={}, file="dir_key_store.go:266"
2025-08-12T11:25:53.464-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T11:35:56.266-04:00 level=ERROR msg=Failed to remove test file, l1=100, path="../uploads/metro2_reports/100", error={
  "Op": "remove",
  "Path": "../uploads/metro2_reports/100/.test_write",
  "Err": 2
}, file="dir_key_store.go:537"
2025-08-12T11:35:56.268-04:00 level=ERROR msg=Failed to save current state to file, file="dir_key_store.go:266", error={}
2025-08-12T12:02:40.692-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T12:02:40.709-04:00 level=ERROR msg=Ignored Email:, file="mail.go:328", <EMAIL>="value"
2025-08-12T12:02:40.726-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T12:02:40.738-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T12:02:40.753-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T12:02:46.159-04:00 level=ERROR msg=Server error, duration="571.913952ms", clientIP="::1", responseSize=288, userID="6RDHxuNP6pT", file="security.go:176", method="POST", path="/v1/checkout/session", status=500
2025-08-12T12:02:57.695-04:00 level=ERROR msg=Server error, clientIP="::1", responseSize=288, userID="6RDHxuNP6pT", file="security.go:176", method="POST", path="/v1/checkout/session", status=500, duration="489.962673ms"
2025-08-12T12:13:31.883-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T12:13:41.988-04:00 level=ERROR msg=Server error, method="POST", path="/v1/checkout/session", status=500, duration="472.234567ms", clientIP="::1", responseSize=288, userID="6RDHxuNP6pT", file="security.go:176"
2025-08-12T12:30:38.866-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T12:30:44.399-04:00 level=ERROR msg=Server error, path="/v1/checkout/session", status=500, duration="660.864166ms", clientIP="::1", responseSize=288, userID="6RDHxuNP6pT", file="security.go:176", method="POST"
2025-08-12T12:51:30.884-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T12:51:40.394-04:00 level=ERROR msg=Server error, clientIP="::1", responseSize=288, userID="6RDHxuNP6pT", file="security.go:176", method="POST", path="/v1/checkout/session", status=500, duration="519.071419ms"
2025-08-12T12:52:00.397-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T12:52:05.327-04:00 level=ERROR msg=Server error, responseSize=288, userID="6RDHxuNP6pT", file="security.go:176", method="POST", path="/v1/checkout/session", status=500, duration="688.182553ms", clientIP="::1"
2025-08-12T12:53:41.909-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T13:05:24.072-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T13:08:14.364-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T13:29:32.918-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-12T13:35:14.826-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T13:39:16.422-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T13:55:41.244-04:00 level=ERROR msg=Failed to generate Metro2 file, error={}, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", file="metro2_report.go:75"
2025-08-12T13:55:41.245-04:00 level=ERROR msg=Server error, responseSize=98, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", file="security.go:176", method="POST", path="/v1/metro2/generate", status=500, duration="22.184185ms", clientIP="::1"
2025-08-12T14:20:15.027-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T14:20:37.311-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T14:20:37.323-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T14:20:37.333-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T14:20:37.346-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T14:20:37.356-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T14:20:37.371-04:00 level=ERROR msg=Ignored Email:, <EMAIL>="value", file="mail.go:328"
2025-08-12T14:34:10.994-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T14:43:22.524-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T14:56:09.619-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-12T17:43:47.494-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:43:47.495-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-12T17:43:48.311-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:43:48.312-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-12T17:43:48.591-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:43:48.593-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-12T17:43:48.931-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:43:48.931-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-12T17:45:17.044-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:45:17.044-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-12T17:45:17.416-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:45:17.418-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-12T17:45:17.666-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:45:17.667-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-12T17:45:18.003-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-12T17:45:18.004-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-13T10:30:47.118-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-13T10:30:47.12-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
2025-08-13T10:30:47.8-04:00 level=ERROR msg=Database configuration not properly initialized, dbName="", file="mongodb.go:217"
2025-08-13T10:30:47.8-04:00 level=ERROR msg=error logging email:, error={}, file="mail.go:401"
