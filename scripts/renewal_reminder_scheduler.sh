#!/bin/bash

# Renewal Reminder Scheduler Script
# This script runs the renewal reminder CLI program
# Designed to be executed by cron daily

# Set script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CLI_BINARY="$PROJECT_ROOT/build/bin/cli_renewal_reminder"
LOG_FILE="$PROJECT_ROOT/logs/renewal_reminder_scheduler.log"
PID_FILE="$PROJECT_ROOT/tmp/renewal_reminder.pid"

# Create necessary directories
mkdir -p "$PROJECT_ROOT/logs"
mkdir -p "$PROJECT_ROOT/tmp"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to cleanup on exit
cleanup() {
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Function to check if another instance is running
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_message "Another instance is already running (PID: $pid). Exiting."
            exit 1
        else
            log_message "Stale PID file found. Removing it."
            rm -f "$PID_FILE"
        fi
    fi
}

# Function to check if binary exists
check_binary() {
    if [ ! -f "$CLI_BINARY" ]; then
        log_message "ERROR: CLI binary not found at $CLI_BINARY"
        log_message "Please run 'make build-cli' to build the CLI tools"
        exit 1
    fi
    
    if [ ! -x "$CLI_BINARY" ]; then
        log_message "ERROR: CLI binary is not executable: $CLI_BINARY"
        exit 1
    fi
}

# Function to validate environment
validate_environment() {
    # Check if config file exists
    local config_file="$PROJECT_ROOT/configs/local.ini"
    if [ ! -f "$config_file" ]; then
        log_message "ERROR: Configuration file not found: $config_file"
        exit 1
    fi
    
    log_message "Environment validation passed"
}

# Main execution function
main() {
    log_message "=== Renewal Reminder Scheduler Cron Job Started ==="
    log_message "Script directory: $SCRIPT_DIR"
    log_message "Project root: $PROJECT_ROOT"
    log_message "CLI binary: $CLI_BINARY"
    log_message "Log file: $LOG_FILE"

    # Check if another instance is running
    check_running

    # Store PID
    echo $$ > "$PID_FILE"
    log_message "Process PID: $$"
    
    # Validate environment
    validate_environment
    
    # Check if binary exists
    check_binary
    
    # Change to project directory
    cd "$PROJECT_ROOT" || {
        log_message "ERROR: Cannot change to project directory: $PROJECT_ROOT"
        exit 1
    }
    
    # Set environment variables
    export RMBASE_FILE_CFG="configs/local.ini"
    
    log_message "Environment variables set:"
    log_message "  RMBASE_FILE_CFG=$RMBASE_FILE_CFG"
    
    # Execute the CLI program
    log_message "Executing renewal reminder CLI program..."
    START_TIME=$(date +%s)
    
    # Run the CLI program from project root directory
    if "$CLI_BINARY" 2>&1 | tee -a "$LOG_FILE"; then
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        log_message "SUCCESS: Renewal reminder completed successfully in ${DURATION} seconds"
        EXIT_CODE=0
    else
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        log_message "ERROR: Renewal reminder failed after ${DURATION} seconds"
        EXIT_CODE=1
    fi
    
    log_message "=== Renewal Reminder Scheduler Cron Job Completed ==="
    exit $EXIT_CODE
}

# Execute main function
main "$@"
