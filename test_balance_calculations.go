package main

import (
	"fmt"
	"strings"
	"time"
)

// 简化的结构体用于测试
type TenantPayment struct {
	Date   time.Time
	Amount float64
}

type Lease struct {
	StartDate             string
	EndDate               string
	RentAmount            float64
	AdditionalMonthlyFees float64
	OwingBalance          float64 // 模拟原始owingBalance（包含所有月份）
}

// 复制calculateCurrentBalance函数用于测试
func calculateCurrentBalance(lease *Lease, payments []*TenantPayment, reportMonth time.Time) float64 {
	// 计算截止到报告月份前一个月的应付租金总额
	// 不包含报告月份及之后的租金，因为还没有到期

	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		// 如果日期解析失败，使用原始owingBalance
		if lease.OwingBalance < 0 {
			return 0.0
		}
		return lease.OwingBalance
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			// 如果结束日期解析失败，忽略结束日期
		}
	}

	// 计算截止到报告月份的应付租金（包含报告月份）
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	// 如果报告月份在租约开始之前，余额为0
	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())
	if reportMonthStart.Before(leaseStartMonth) {
		return 0.0
	}

	// 计算应付月数（从租约开始到报告月份，包含报告月份）
	monthsOwed := 0
	current := leaseStartMonth
	for current.Before(reportMonthStart) || current.Equal(reportMonthStart) {
		// 检查是否在租约期间内
		if !leaseEnd.IsZero() {
			leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
			if current.After(leaseEndMonth) {
				break
			}
		}
		monthsOwed++
		current = current.AddDate(0, 1, 0)
	}

	// 计算总应付金额
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees
	totalOwed := float64(monthsOwed) * monthlyRent

	// 计算总已付金额（截止到报告月份，包含报告月份）
	totalPaid := 0.0
	for _, payment := range payments {
		paymentMonth := time.Date(payment.Date.Year(), payment.Date.Month(), 1, 0, 0, 0, 0, payment.Date.Location())
		// 计算报告月份及之前的付款
		if paymentMonth.Before(reportMonthStart) || paymentMonth.Equal(reportMonthStart) {
			totalPaid += payment.Amount
		}
	}

	// 计算当前余额
	currentBalance := totalOwed - totalPaid

	// Metro2 规则：负数余额显示为 0（表示多付款）
	if currentBalance < 0 {
		return 0.0
	}

	return currentBalance
}

// 简化的calculateAccountStatue函数用于测试
func calculateAccountStatue(lease *Lease, payments []*TenantPayment, reportMonth time.Time) string {
	// 这里简化逻辑，实际应该使用完整的account status计算
	// 为了测试，我们假设如果有未付款就返回逾期状态
	currentBalance := calculateCurrentBalance(lease, payments, reportMonth)
	if currentBalance > 0 {
		return "71" // 假设逾期状态
	}
	return "11" // 当前状态
}

// 复制calculateAmountPastDue函数用于测试
func calculateAmountPastDue(lease *Lease, payments []*TenantPayment, reportMonth time.Time) float64 {
	// 检查账户状态，如果当前账户则无逾期
	accountStatus := calculateAccountStatue(lease, payments, reportMonth)
	if accountStatus == "11" {
		return 0 // 当前账户，无逾期
	}

	// 检查租约状态
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return 0
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			// 如果结束日期解析失败，忽略结束日期
		}
	}

	// 如果租约已结束或未开始，无逾期
	if (!leaseEnd.IsZero() && reportMonth.After(leaseEnd)) || reportMonth.Before(leaseStart) {
		return 0
	}

	// 计算截止到报告月份前一个月的逾期金额
	// 不包含报告月份及之后的租金，因为还没有到期
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	// 使用与payment history相同的逻辑计算逾期金额
	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())
	firstPaymentMonth := leaseStartMonth
	lastHistoryMonth := reportMonthStart.AddDate(0, -1, 0) // 报告月份的前一个月

	// 如果最后历史月份在第一个付款月之前，无逾期
	if lastHistoryMonth.Before(firstPaymentMonth) {
		return 0
	}

	monthsFromFirstPayment := 0
	current := firstPaymentMonth
	for current.Before(lastHistoryMonth) || current.Equal(lastHistoryMonth) {
		monthsFromFirstPayment++
		current = current.AddDate(0, 1, 0)
	}

	// 限制月数不超过24
	if monthsFromFirstPayment > 24 {
		monthsFromFirstPayment = 24
	}

	// 计算累积逾期金额
	cumulativeOverdueAmount := 0.0
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 从最早的月份开始，向后检查每个月的付款情况
	for i := monthsFromFirstPayment - 1; i >= 0; i-- {
		// 计算当前位置对应的月份（从报告月份前一个月向前推）
		currentMonth := lastHistoryMonth.AddDate(0, -i, 0)

		// 检查该月份是否在有效付款期间内
		if currentMonth.Before(firstPaymentMonth) {
			continue
		}

		if !leaseEnd.IsZero() {
			leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
			if currentMonth.After(leaseEndMonth) {
				continue
			}
		}

		// 检查该月份是否有足额付款
		monthlyPayment := 0.0
		for _, payment := range payments {
			paymentMonth := time.Date(payment.Date.Year(), payment.Date.Month(), 1, 0, 0, 0, 0, payment.Date.Location())
			if paymentMonth.Equal(currentMonth) {
				monthlyPayment += payment.Amount
			}
		}

		if monthlyPayment < monthlyRent {
			// 未足额付款，累积逾期金额
			cumulativeOverdueAmount += (monthlyRent - monthlyPayment)
		} else {
			// 足额付款，重置累积逾期金额
			cumulativeOverdueAmount = 0
		}
	}

	return cumulativeOverdueAmount
}

// 测试用例结构
type TestCase struct {
	name            string
	lease           *Lease
	payments        []*TenantPayment
	reportMonth     time.Time
	expectedBalance float64
	expectedPastDue float64
	description     string
}

func main() {
	fmt.Println("测试Current Balance和Amount Past Due计算逻辑")
	fmt.Println("验证是否正确排除了报告月份及之后的租金")
	fmt.Println("=" + strings.Repeat("=", 80))

	// 测试场景：验证Current Balance和Amount Past Due的计算逻辑
	testCases := []TestCase{
		{
			name: "场景1: 7月报告，5-7月无付款",
			lease: &Lease{
				StartDate:             "2024-05-01",
				EndDate:               "",
				RentAmount:            1000.0,
				AdditionalMonthlyFees: 0.0,
				OwingBalance:          3000.0,
			},
			payments:        []*TenantPayment{},
			reportMonth:     time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC),
			expectedBalance: 3000.0, // 5月+6月+7月 = 3个月租金
			expectedPastDue: 2000.0, // 只有5月+6月逾期，7月还没超过30天
			description:     "Current Balance包含7月，Past Due不包含7月",
		},
		{
			name: "场景2: 7月报告，5-7月全付款",
			lease: &Lease{
				StartDate:             "2024-05-01",
				EndDate:               "",
				RentAmount:            1000.0,
				AdditionalMonthlyFees: 0.0,
				OwingBalance:          0.0,
			},
			payments: []*TenantPayment{
				{Date: time.Date(2024, 5, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0},
				{Date: time.Date(2024, 6, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0},
				{Date: time.Date(2024, 7, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0},
			},
			reportMonth:     time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC),
			expectedBalance: 0.0, // 全部付清
			expectedPastDue: 0.0, // 无逾期
			description:     "全部付款，余额应为0",
		},
		{
			name: "场景3: 7月报告，5月付款，6-7月未付",
			lease: &Lease{
				StartDate:             "2024-05-01",
				EndDate:               "",
				RentAmount:            1000.0,
				AdditionalMonthlyFees: 0.0,
				OwingBalance:          2000.0,
			},
			payments: []*TenantPayment{
				{Date: time.Date(2024, 5, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0},
			},
			reportMonth:     time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC),
			expectedBalance: 2000.0, // 欠6月+7月租金
			expectedPastDue: 1000.0, // 只有6月逾期，7月还没超过30天
			description:     "6月逾期，7月未到逾期期限",
		},
		{
			name: "场景4: 8月报告，5-7月无付款",
			lease: &Lease{
				StartDate:             "2024-05-01",
				EndDate:               "",
				RentAmount:            1000.0,
				AdditionalMonthlyFees: 0.0,
				OwingBalance:          4000.0,
			},
			payments:        []*TenantPayment{},
			reportMonth:     time.Date(2024, 8, 1, 0, 0, 0, 0, time.UTC),
			expectedBalance: 4000.0, // 5月+6月+7月+8月 = 4个月租金
			expectedPastDue: 3000.0, // 5月+6月+7月逾期，8月还没超过30天
			description:     "8月报告应包含8月，Past Due不包含8月",
		},
		{
			name: "场景5: 7月报告，包含7月付款（应该计算）",
			lease: &Lease{
				StartDate:             "2024-05-01",
				EndDate:               "",
				RentAmount:            1000.0,
				AdditionalMonthlyFees: 0.0,
				OwingBalance:          1000.0,
			},
			payments: []*TenantPayment{
				{Date: time.Date(2024, 5, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0},
				{Date: time.Date(2024, 6, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0},
				{Date: time.Date(2024, 7, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0}, // 7月付款应该计算
			},
			reportMonth:     time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC),
			expectedBalance: 0.0, // 全部付清（7月付款计算在内）
			expectedPastDue: 0.0, // 无逾期
			description:     "7月付款应该影响7月报告的计算",
		},
		{
			name: "场景6: 7月报告，包含8月付款（不应计算）",
			lease: &Lease{
				StartDate:             "2024-05-01",
				EndDate:               "",
				RentAmount:            1000.0,
				AdditionalMonthlyFees: 0.0,
				OwingBalance:          3000.0,
			},
			payments: []*TenantPayment{
				{Date: time.Date(2024, 5, 15, 0, 0, 0, 0, time.UTC), Amount: 1000.0},
				{Date: time.Date(2024, 8, 15, 0, 0, 0, 0, time.UTC), Amount: 2000.0}, // 8月付款不应计算
			},
			reportMonth:     time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC),
			expectedBalance: 2000.0, // 欠6月+7月租金（8月付款不计算）
			expectedPastDue: 1000.0, // 只有6月逾期，7月还没超过30天
			description:     "未来月份的付款不应影响当前报告",
		},
	}

	fmt.Printf("%-10s %-40s %-15s %-15s %-15s %-15s %-8s\n",
		"场景", "描述", "期望余额", "实际余额", "期望逾期", "实际逾期", "结果")
	fmt.Println(strings.Repeat("-", 120))

	allPassed := true
	for i, tc := range testCases {
		actualBalance := calculateCurrentBalance(tc.lease, tc.payments, tc.reportMonth)
		actualPastDue := calculateAmountPastDue(tc.lease, tc.payments, tc.reportMonth)

		balancePassed := actualBalance == tc.expectedBalance
		pastDuePassed := actualPastDue == tc.expectedPastDue
		testPassed := balancePassed && pastDuePassed

		if !testPassed {
			allPassed = false
		}

		result := "PASS"
		if !testPassed {
			result = "FAIL"
		}

		fmt.Printf("%-10s %-40s %-15.2f %-15.2f %-15.2f %-15.2f %-8s\n",
			fmt.Sprintf("场景%d", i+1), tc.description, tc.expectedBalance, actualBalance,
			tc.expectedPastDue, actualPastDue, result)

		if !testPassed {
			fmt.Printf("  详细: %s\n", tc.name)
			if !balancePassed {
				fmt.Printf("  余额不匹配: 期望%.2f, 实际%.2f\n", tc.expectedBalance, actualBalance)
			}
			if !pastDuePassed {
				fmt.Printf("  逾期不匹配: 期望%.2f, 实际%.2f\n", tc.expectedPastDue, actualPastDue)
			}
		}
	}

	fmt.Println(strings.Repeat("-", 120))
	if allPassed {
		fmt.Println("✅ 所有测试通过！Current Balance和Amount Past Due计算逻辑正确。")
	} else {
		fmt.Println("❌ 部分测试失败！需要检查计算逻辑。")
	}
}
