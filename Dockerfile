FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
RUN addgroup -S appgroup
RUN adduser -S -u 1000 -G appgroup appuser
WORKDIR /app
RUN mkdir -p /app/configs /app/web/dist /app/logs /app/docs
RUN chown -R appuser:appgroup /app
COPY bin/rent_report /app/server
COPY web/dist/ /app/web/dist/
COPY configs/ /app/configs/
COPY docs/ /app/docs/
RUN chown -R appuser:appgroup /app
RUN chmod +x /app/server
USER appuser
EXPOSE 8089
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:8089/health || exit 1
CMD ["/app/server", "--config", "/app/configs/local.ini"]
