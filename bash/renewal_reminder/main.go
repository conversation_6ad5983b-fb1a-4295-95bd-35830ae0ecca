package main

import (
	"context"
	"fmt"
	"os"
	"rent_report/utils"
	"rent_report/utils/encryption"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/sub"
	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	// Set default config file if none specified
	if os.Getenv("RMBASE_FILE_CFG") == "" {
		os.Setenv("RMBASE_FILE_CFG", "configs/local.ini")
	}

	// Initialize configuration
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		fmt.Printf("Failed to initialize logging: %v\n", err)
		os.Exit(1)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		fmt.Printf("Failed to initialize MongoDB: %v\n", err)
		os.Exit(1)
	}

	// Initialize encryption
	encryption.InitEncryption()

	// Initialize Stripe
	stripeKey := goconfig.Config("stripe.secret_key")
	if stripeKey == nil {
		golog.Error("Stripe secret key not found in config")
		os.Exit(1)
	}
	stripe.Key = stripeKey.(string)

	// 检查配置是否启用
	enabled := goconfig.Config("renewalReminderEmail.enabled")
	if enabled == nil || !enabled.(bool) {
		golog.Info("Renewal reminder email is disabled in config")
		return
	}

	golog.Info("=== Renewal Reminder CLI Started ===")

	// 获取配置
	reminderDays := 7 // 默认7天
	if days := goconfig.Config("renewalReminderEmail.reminderDays"); days != nil {
		if d, ok := days.(int); ok {
			reminderDays = d
		}
	}

	testMode := false
	if test := goconfig.Config("renewalReminderEmail.testMode"); test != nil {
		if t, ok := test.(bool); ok {
			testMode = t
		}
	}

	golog.Info("Configuration loaded",
		"reminderDays", reminderDays,
		"testMode", testMode)

	// 执行续订提醒处理
	if err := processRenewalReminders(reminderDays, testMode); err != nil {
		golog.Error("Failed to process renewal reminders", "error", err)
		os.Exit(1)
	}

	golog.Info("=== Renewal Reminder CLI Completed Successfully ===")
}

// processRenewalReminders 处理续订提醒
func processRenewalReminders(reminderDays int, testMode bool) error {
	ctx := context.Background()

	golog.Info("Starting renewal reminder processing",
		"reminderDays", reminderDays,
		"testMode", testMode)

	// 获取所有活跃的用户订阅
	userSubColl := gomongo.Coll("rr", "usersub")
	cursor, err := userSubColl.Find(ctx, bson.M{
		"sts": "active",
	})
	if err != nil {
		return fmt.Errorf("failed to find active subscriptions: %v", err)
	}
	defer cursor.Close(ctx)

	processedCount := 0
	sentCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var userSub bson.M
		if err := cursor.Decode(&userSub); err != nil {
			golog.Error("Failed to decode user subscription", "error", err)
			errorCount++
			continue
		}

		processedCount++

		// 处理单个订阅
		sent, err := processSubscriptionReminder(ctx, userSub, reminderDays, testMode)
		if err != nil {
			golog.Error("Failed to process subscription reminder",
				"error", err,
				"userSubId", userSub["_id"],
				"uid", userSub["uid"])
			errorCount++
		} else if sent {
			sentCount++
		}
	}

	golog.Info("Renewal reminder processing completed",
		"processedCount", processedCount,
		"sentCount", sentCount,
		"errorCount", errorCount)

	return nil
}

// processSubscriptionReminder 处理单个订阅的续订提醒
func processSubscriptionReminder(ctx context.Context, userSub bson.M, reminderDays int, testMode bool) (bool, error) {
	stripeSubId, ok := userSub["stripeSubId"].(string)
	if !ok || stripeSubId == "" {
		return false, fmt.Errorf("missing or invalid stripeSubId")
	}

	uid, ok := userSub["uid"].(string)
	if !ok || uid == "" {
		return false, fmt.Errorf("missing or invalid uid")
	}

	// 从 Stripe 获取订阅信息
	subscription, err := sub.Get(stripeSubId, nil)
	if err != nil {
		return false, fmt.Errorf("failed to get Stripe subscription: %v", err)
	}

	// 检查订阅状态
	if subscription.Status != stripe.SubscriptionStatusActive {
		golog.Debug("Subscription is not active, skipping",
			"stripeSubId", stripeSubId,
			"status", subscription.Status)
		return false, nil
	}

	// 计算下次计费日期
	nextBillingDate := time.Unix(subscription.CurrentPeriodEnd, 0)

	// 计算提醒日期（提前N天）
	reminderDate := nextBillingDate.AddDate(0, 0, -reminderDays)
	now := time.Now()

	// 检查是否应该发送提醒
	if testMode {
		golog.Info("TEST MODE: Sending reminder regardless of date",
			"uid", uid,
			"nextBillingDate", nextBillingDate.Format("2006-01-02"),
			"reminderDate", reminderDate.Format("2006-01-02"))
	} else {
		// 检查是否在提醒日期范围内（当天或已过期但未超过1天）
		if now.Before(reminderDate) || now.After(reminderDate.AddDate(0, 0, 1)) {
			golog.Debug("Not in reminder date range, skipping",
				"uid", uid,
				"now", now.Format("2006-01-02"),
				"reminderDate", reminderDate.Format("2006-01-02"),
				"nextBillingDate", nextBillingDate.Format("2006-01-02"))
			return false, nil
		}
	}

	// 检查是否已经发送过提醒
	if hasReminderBeenSent(ctx, uid, stripeSubId, nextBillingDate) {
		golog.Debug("Reminder already sent for this billing cycle, skipping",
			"uid", uid,
			"stripeSubId", stripeSubId,
			"nextBillingDate", nextBillingDate.Format("2006-01-02"))
		return false, nil
	}

	// 获取用户信息
	userColl := gomongo.Coll("rr", "users")
	var user bson.M
	if err := userColl.FindOne(ctx, bson.M{"_id": uid}).Decode(&user); err != nil {
		return false, fmt.Errorf("failed to get user: %v", err)
	}

	email, ok := user["email"].(string)
	if !ok || email == "" {
		return false, fmt.Errorf("user has no email address")
	}

	firstName := ""
	lastName := ""
	if name, ok := user["usrNm"].(string); ok {
		firstName = name
		// 如果需要分离姓和名，可以在这里处理
		// 目前假设 usrNm 是全名，lastName 留空
	}

	// 获取订阅计划信息
	planName := "Professional"   // 默认值
	subscriptionCost := "$17.99" // 默认值，可以从数据库中获取

	if planId, ok := userSub["planId"].(string); ok && planId != "" {
		subplanColl := gomongo.Coll("rr", "subplan")
		var subplan bson.M
		if err := subplanColl.FindOne(ctx, bson.M{"_id": planId}).Decode(&subplan); err == nil {
			if nm, ok := subplan["nm"].(string); ok && nm != "" {
				planName = nm
			}
		}
	}

	// 发送提醒邮件
	golog.Info("Sending renewal reminder email",
		"uid", uid,
		"email", email,
		"planName", planName,
		"nextBillingDate", nextBillingDate.Format("2006-01-02"))

	if err := utils.SendRenewalReminderEmail(email, firstName, lastName, planName, subscriptionCost, nextBillingDate); err != nil {
		return false, fmt.Errorf("failed to send renewal reminder email: %v", err)
	}

	// 记录已发送提醒
	if err := recordReminderSent(ctx, uid, stripeSubId, nextBillingDate); err != nil {
		golog.Error("Failed to record reminder sent", "error", err, "uid", uid)
		// 不返回错误，因为邮件已经发送成功
	}

	golog.Info("Renewal reminder email sent successfully",
		"uid", uid,
		"email", email,
		"planName", planName)

	return true, nil
}

// hasReminderBeenSent 检查是否已经为此计费周期发送过提醒
func hasReminderBeenSent(ctx context.Context, uid, stripeSubId string, nextBillingDate time.Time) bool {
	reminderColl := gomongo.Coll("rr", "renewal_reminders")

	// 查找是否存在相同的提醒记录
	count, err := reminderColl.CountDocuments(ctx, bson.M{
		"uid":             uid,
		"stripeSubId":     stripeSubId,
		"nextBillingDate": nextBillingDate,
	})

	return err == nil && count > 0
}

// recordReminderSent 记录已发送提醒
func recordReminderSent(ctx context.Context, uid, stripeSubId string, nextBillingDate time.Time) error {
	reminderColl := gomongo.Coll("rr", "renewal_reminders")

	reminder := bson.M{
		"_id":             utils.GenerateNanoID(),
		"uid":             uid,
		"stripeSubId":     stripeSubId,
		"nextBillingDate": nextBillingDate,
		"sentAt":          time.Now(),
	}

	_, err := reminderColl.InsertOne(ctx, reminder)
	return err
}
