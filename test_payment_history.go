package main

import (
	"fmt"
	"strings"
	"time"
)

// 简化的结构体用于测试
type TenantPayment struct {
	Date   time.Time
	Amount float64
}

type Lease struct {
	StartDate             string
	EndDate               string
	RentAmount            float64
	AdditionalMonthlyFees float64
}

// 复制calculatePaymentHistory函数用于测试
func calculatePaymentHistory(payments []*TenantPayment, lease *Lease, reportMonth time.Time) string {
	// 解析租约开始和结束日期
	leaseStart, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return "BBBBBBBBBBBBBBBBBBBBBBBB"
	}

	leaseEnd := time.Time{}
	if lease.EndDate != "" {
		leaseEnd, err = time.Parse("2006-01-02", lease.EndDate)
		if err != nil {
			return "BBBBBBBBBBBBBBBBBBBBBBBB"
		}
	}

	// 初始化24个月的历史记录，全部设为B
	history := make([]string, 24)
	for i := 0; i < 24; i++ {
		history[i] = "B"
	}

	// 如果报告月份在租约开始之前，返回全B
	leaseStartMonth := time.Date(leaseStart.Year(), leaseStart.Month(), 1, 0, 0, 0, 0, leaseStart.Location())
	reportMonthStart := time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	if reportMonthStart.Before(leaseStartMonth) {
		return strings.Join(history, "")
	}

	// 如果租约已结束且报告月份在结束日期之后，返回全B
	if !leaseEnd.IsZero() {
		leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
		if reportMonthStart.After(leaseEndMonth) {
			return strings.Join(history, "")
		}
	}

	// 计算从租约开始的下一个月到报告月份的月数
	// 租约开始月不需要付款记录，从下一个月开始
	firstPaymentMonth := leaseStartMonth.AddDate(0, 1, 0)

	// 如果报告月份在第一个付款月之前，返回全B
	if reportMonthStart.Before(firstPaymentMonth) {
		return strings.Join(history, "")
	}

	monthsFromFirstPayment := 0
	current := firstPaymentMonth
	for current.Before(reportMonthStart) || current.Equal(reportMonthStart) {
		monthsFromFirstPayment++
		current = current.AddDate(0, 1, 0)
	}

	// 限制月数不超过24
	if monthsFromFirstPayment > 24 {
		monthsFromFirstPayment = 24
	}

	// 首先从最早的月份开始，计算每个月的付款状态
	monthlyStatuses := make([]string, monthsFromFirstPayment)

	// 从最早的付款月份开始，向后检查每个月的付款情况
	for i := monthsFromFirstPayment - 1; i >= 0; i-- {
		// 计算当前位置对应的月份（从报告月份向前推）
		currentMonth := reportMonthStart.AddDate(0, -i, 0)

		// 检查该月份是否在有效付款期间内
		if currentMonth.Before(firstPaymentMonth) {
			monthlyStatuses[i] = "B"
			continue
		}

		if !leaseEnd.IsZero() {
			leaseEndMonth := time.Date(leaseEnd.Year(), leaseEnd.Month(), 1, 0, 0, 0, 0, leaseEnd.Location())
			if currentMonth.After(leaseEndMonth) {
				monthlyStatuses[i] = "B"
				continue
			}
		}

		// 检查该月份是否有足额付款
		monthlyPayment := 0.0
		for _, payment := range payments {
			paymentMonth := time.Date(payment.Date.Year(), payment.Date.Month(), 1, 0, 0, 0, 0, payment.Date.Location())
			if paymentMonth.Equal(currentMonth) {
				monthlyPayment += payment.Amount
			}
		}

		monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

		if monthlyPayment >= monthlyRent {
			monthlyStatuses[i] = "0" // 足额付款
		} else {
			monthlyStatuses[i] = "unpaid" // 未足额付款，稍后计算逾期程度
		}
	}

	// 现在计算逾期程度：从最早的月份开始累积逾期月数
	cumulativeOverdueMonths := 0
	for i := monthsFromFirstPayment - 1; i >= 0; i-- {
		if monthlyStatuses[i] == "0" {
			// 足额付款，重置累积逾期月数
			cumulativeOverdueMonths = 0
			history[i] = "0"
		} else if monthlyStatuses[i] == "unpaid" {
			// 未足额付款，累积逾期月数增加
			cumulativeOverdueMonths++

			// 根据累积逾期月数确定逾期程度
			switch {
			case cumulativeOverdueMonths == 1:
				history[i] = "1" // 30-59天逾期
			case cumulativeOverdueMonths == 2:
				history[i] = "2" // 60-89天逾期
			case cumulativeOverdueMonths == 3:
				history[i] = "3" // 90-119天逾期
			case cumulativeOverdueMonths == 4:
				history[i] = "4" // 120-149天逾期
			case cumulativeOverdueMonths == 5:
				history[i] = "5" // 150-179天逾期
			default:
				history[i] = "6" // 180+天逾期
			}
		} else {
			// B状态
			history[i] = monthlyStatuses[i]
		}
	}

	return strings.Join(history, "")
}

func main() {
	// 测试案例1：5月1号入住，6月报告，无付款
	lease1 := &Lease{
		StartDate:             "2024-05-01",
		EndDate:               "",
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 0.0,
	}

	reportMonth1, _ := time.Parse("2006-01-02", "2024-06-01")
	payments1 := []*TenantPayment{} // 无付款

	result1 := calculatePaymentHistory(payments1, lease1, reportMonth1)
	fmt.Printf("测试1 - 5月1号入住，6月报告，无付款: %s\n", result1)
	fmt.Printf("期望: 1BBBBBBBBBBBBBBBBBBBBBBB\n")
	fmt.Printf("匹配: %t\n\n", result1 == "1BBBBBBBBBBBBBBBBBBBBBBB")

	// 测试案例2：5月1号入住，7月报告，无付款
	reportMonth2, _ := time.Parse("2006-01-02", "2024-07-01")
	payments2 := []*TenantPayment{} // 无付款

	result2 := calculatePaymentHistory(payments2, lease1, reportMonth2)
	fmt.Printf("测试2 - 5月1号入住，7月报告，无付款: %s\n", result2)
	fmt.Printf("期望: 21BBBBBBBBBBBBBBBBBBBBBB\n")
	fmt.Printf("匹配: %t\n\n", result2 == "21BBBBBBBBBBBBBBBBBBBBBB")
}
